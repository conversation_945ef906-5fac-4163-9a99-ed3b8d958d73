"""
Action Table Metrics Calculator

This module provides functions to calculate metrics for various action categories
from survey responses, including scores and sentiment percentages.
"""

from decimal import Decimal



def calculate_sentiment_percentages(survey_responses):
    """Calculate overall sentiment percentages."""
    total_with_sentiment = survey_responses[survey_responses['predicted_sentiment'].notna()].shape[0]  
    
    if total_with_sentiment == 0:
        return Decimal('0.00'), Decimal('0.00'), Decimal('0.00')

    positive_count = survey_responses[survey_responses['predicted_sentiment'] == "Positive"].shape[0]  
    neutral_count = survey_responses[survey_responses['predicted_sentiment'] == "Neutral"].shape[0]  
    negative_count = survey_responses[survey_responses['predicted_sentiment'] == "Negative"].shape[0] 

    positive_pct = (Decimal(positive_count) / Decimal(total_with_sentiment)) * 100
    neutral_pct = (Decimal(neutral_count) / Decimal(total_with_sentiment)) * 100
    negative_pct = (Decimal(negative_count) / Decimal(total_with_sentiment)) * 100

    return (
        positive_pct.quantize(Decimal('0.01')),
        neutral_pct.quantize(Decimal('0.01')),
        negative_pct.quantize(Decimal('0.01'))
    )

def calculate_category_sentiment_percentages(survey_responses, question_numbers):
    """Calculate sentiment percentages for a specific category of questions."""
    category_responses = survey_responses[survey_responses['question_number'].isin(question_numbers)]
    return calculate_sentiment_percentages(category_responses)

def calculate_action_score(positive_pct, neutral_pct, negative_pct):
    """Calculate action score based on sentiment percentages using same weights as DEI score."""
    score = (
        (positive_pct * Decimal('1.0')) +
        (neutral_pct * Decimal('0.5')) +
        (negative_pct * Decimal('-0.1'))
    )
    return score.quantize(Decimal('0.01'))

def calculate_communication_metrics(survey_responses, question_numbers):
    """Calculate communication metrics using AI-predicted question numbers."""
    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, question_numbers
    )
    score = calculate_action_score(positive_pct, neutral_pct, negative_pct)

    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score
    }

def calculate_leadership_effectiveness_metrics(survey_responses, question_numbers):
    """Calculate leadership effectiveness metrics using AI-predicted question numbers."""
    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, question_numbers
    )
    score = calculate_action_score(positive_pct, neutral_pct, negative_pct)

    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score
    }

def calculate_work_life_balance_metrics(survey_responses, question_numbers):
    """Calculate work-life balance metrics using AI-predicted question numbers."""
    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, question_numbers
    )
    score = calculate_action_score(positive_pct, neutral_pct, negative_pct)

    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score
    }

def calculate_career_development_metrics(survey_responses, question_numbers):
    """Calculate career development metrics using AI-predicted question numbers."""
    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, question_numbers
    )
    score = calculate_action_score(positive_pct, neutral_pct, negative_pct)

    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score
    }

def calculate_recognition_rewards_metrics(survey_responses, question_numbers):
    """Calculate recognition and rewards metrics using AI-predicted question numbers."""
    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, question_numbers
    )
    score = calculate_action_score(positive_pct, neutral_pct, negative_pct)

    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score
    }

def calculate_employee_engagement_metrics(survey_responses, question_numbers):
    """Calculate employee engagement metrics using AI-predicted question numbers."""
    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, question_numbers
    )
    score = calculate_action_score(positive_pct, neutral_pct, negative_pct)

    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score
    }

def calculate_workplace_environment_metrics(survey_responses, question_numbers):
    """Calculate workplace environment metrics using AI-predicted question numbers."""
    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, question_numbers
    )
    score = calculate_action_score(positive_pct, neutral_pct, negative_pct)

    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score
    }

def calculate_inclusion_diversity_metrics(survey_responses, question_numbers):
    """Calculate inclusion and diversity metrics using AI-predicted question numbers."""
    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, question_numbers
    )
    score = calculate_action_score(positive_pct, neutral_pct, negative_pct)

    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score
    }

def calculate_compensation_transparency_metrics(survey_responses, question_numbers):
    """Calculate compensation transparency metrics using AI-predicted question numbers."""
    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, question_numbers
    )
    score = calculate_action_score(positive_pct, neutral_pct, negative_pct)

    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score
    }

def calculate_feedback_mechanisms_metrics(survey_responses, question_numbers):
    """Calculate feedback mechanisms metrics using AI-predicted question numbers."""
    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, question_numbers
    )
    score = calculate_action_score(positive_pct, neutral_pct, negative_pct)

    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score
    }

def calculate_organizational_transparency_metrics(survey_responses, question_numbers):
    """Calculate organizational transparency metrics using AI-predicted question numbers."""
    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, question_numbers
    )
    score = calculate_action_score(positive_pct, neutral_pct, negative_pct)

    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score
    }

def calculate_manager_employee_relationship_metrics(survey_responses, question_numbers):
    """Calculate manager-employee relationship metrics using AI-predicted question numbers."""
    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, question_numbers
    )
    score = calculate_action_score(positive_pct, neutral_pct, negative_pct)

    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score
    }

def calculate_psychological_safety_metrics(survey_responses, question_numbers):
    """Calculate psychological safety metrics using AI-predicted question numbers."""
    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, question_numbers
    )
    score = calculate_action_score(positive_pct, neutral_pct, negative_pct)

    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score
    }

def calculate_mission_values_alignment_metrics(survey_responses, question_numbers):
    """Calculate mission and values alignment metrics using AI-predicted question numbers."""
    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, question_numbers
    )
    score = calculate_action_score(positive_pct, neutral_pct, negative_pct)

    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score
    }

def calculate_innovation_creativity_metrics(survey_responses, question_numbers):
    """Calculate innovation and creativity metrics using AI-predicted question numbers."""
    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, question_numbers
    )
    score = calculate_action_score(positive_pct, neutral_pct, negative_pct)

    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score
    }

def calculate_all_action_metrics(survey_responses, question_numbers_dict):
    """Calculate metrics for all action categories using AI-predicted question numbers."""
    metrics = {}

    if 'communication' in question_numbers_dict:
        metrics['communication'] = calculate_communication_metrics(survey_responses, question_numbers_dict['communication'])
    if 'leadership_effectiveness' in question_numbers_dict:
        metrics['leadership_effectiveness'] = calculate_leadership_effectiveness_metrics(survey_responses, question_numbers_dict['leadership_effectiveness'])
    if 'work_life_balance' in question_numbers_dict:
        metrics['work_life_balance'] = calculate_work_life_balance_metrics(survey_responses, question_numbers_dict['work_life_balance'])
    if 'career_development' in question_numbers_dict:
        metrics['career_development'] = calculate_career_development_metrics(survey_responses, question_numbers_dict['career_development'])
    if 'recognition_rewards' in question_numbers_dict:
        metrics['recognition_rewards'] = calculate_recognition_rewards_metrics(survey_responses, question_numbers_dict['recognition_rewards'])
    if 'employee_engagement' in question_numbers_dict:
        metrics['employee_engagement'] = calculate_employee_engagement_metrics(survey_responses, question_numbers_dict['employee_engagement'])
    if 'workplace_environment' in question_numbers_dict:
        metrics['workplace_environment'] = calculate_workplace_environment_metrics(survey_responses, question_numbers_dict['workplace_environment'])
    if 'inclusion_diversity' in question_numbers_dict:
        metrics['inclusion_diversity'] = calculate_inclusion_diversity_metrics(survey_responses, question_numbers_dict['inclusion_diversity'])
    if 'compensation_transparency' in question_numbers_dict:
        metrics['compensation_transparency'] = calculate_compensation_transparency_metrics(survey_responses, question_numbers_dict['compensation_transparency'])
    if 'feedback_mechanisms' in question_numbers_dict:
        metrics['feedback_mechanisms'] = calculate_feedback_mechanisms_metrics(survey_responses, question_numbers_dict['feedback_mechanisms'])
    if 'organizational_transparency' in question_numbers_dict:
        metrics['organizational_transparency'] = calculate_organizational_transparency_metrics(survey_responses, question_numbers_dict['organizational_transparency'])
    if 'manager_employee_relationship' in question_numbers_dict:
        metrics['manager_employee_relationship'] = calculate_manager_employee_relationship_metrics(survey_responses, question_numbers_dict['manager_employee_relationship'])
    if 'psychological_safety' in question_numbers_dict:
        metrics['psychological_safety'] = calculate_psychological_safety_metrics(survey_responses, question_numbers_dict['psychological_safety'])
    if 'mission_values_alignment' in question_numbers_dict:
        metrics['mission_values_alignment'] = calculate_mission_values_alignment_metrics(survey_responses, question_numbers_dict['mission_values_alignment'])
    if 'innovation_creativity' in question_numbers_dict:
        metrics['innovation_creativity'] = calculate_innovation_creativity_metrics(survey_responses, question_numbers_dict['innovation_creativity'])

    return metrics


