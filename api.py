import boto3
from botocore.exceptions import ClientError
from flask import Flask, render_template, request, redirect, send_file, url_for, jsonify
import pandas as pd
from datetime import datetime, timedelta
import mysql.connector
import requests
from werkzeug.utils import secure_filename
from connect1  import process_category_if_score
from action_page_conditions import (evaluate_communication,evaluate_career_development,evaluate_compensation_transparency,
                                    evaluate_employee_engagement,evaluate_feedback_mechanisms,evaluate_inclusion_diversity,
                                    evaluate_innovation_creativity,evaluate_leadership_effectiveness,evaluate_manager_employee_relationship,
                                    evaluate_mission_values_alignment,evaluate_organizational_transparency,evaluate_psychological_safety,
                                    evaluate_recognition_rewards,evaluate_work_life_balance,evaluate_workplace_environment)
from automatic_category_processor import generate_category_report, get_category_insights
from key_insights import get_insights_from_scores, get_industry_benchmark_percentages

# AWS S3 Configuration
AWS_ACCESS_KEY = '********************'
AWS_SECRET_KEY = 'Gn9g5TW6nv7wIFlydeD9qFTnkb7T/TRpG1sqCdW8'
AWS_BUCKET_NAME = 'amazing-place-to-work'
AWS_REGION = 'ap-south-1'

# Initialize S3 client
s3_client = boto3.client(
    's3',
    aws_access_key_id=AWS_ACCESS_KEY,
    aws_secret_access_key=AWS_SECRET_KEY,
    region_name=AWS_REGION
)


app = Flask(__name__, template_folder='templates')

# Database connection function
def get_db_connection():
    try:
        return mysql.connector.connect(
            host="fz-website-registration.c9uy0agkwmgg.ap-south-1.rds.amazonaws.com",
            user="admin",
            password="master123",
            database='registration',
            # Add connection parameters to help with cursor handling
            consume_results=True,
            autocommit=True
        )
    except mysql.connector.Error as err:
        print(f"Error connecting to database: {err}")
        raise

# Initialize connection
conn = get_db_connection()

# File upload configurations
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg'}
MAX_CONTENT_LENGTH = 2 * 1024 * 1024  # 2MB max file size

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS



import uuid
# MySQL connection
db = mysql.connector.connect(
    host="fz-website-registration.c9uy0agkwmgg.ap-south-1.rds.amazonaws.com",
    user="admin",
    password="master123",
    database="registration"
)
cursor = db.cursor()

@app.route('/add_question/<email>', methods=['POST'])
def add_question(email):
    question = request.form['question']
    option_1 = request.form['option_1']
    option_2 = request.form['option_2']
    option_3 = request.form['option_3']
    option_4 = request.form['option_4']

    # Get the next available question number
    cursor.execute("SELECT MAX(question_number) FROM employee_questions WHERE email = %s", (email,))
    result = cursor.fetchone()
    next_question_number = (result[0] or 0) + 1

    cursor.execute("""
        INSERT INTO employee_questions (
            email, question_number, question_text, option_1, option_2, option_3, option_4
        ) VALUES (%s, %s, %s, %s, %s, %s, %s)
    """, (email, next_question_number, question, option_1, option_2, option_3, option_4))

    db.commit()
    return redirect(f'/view_questions/{email}')


@app.route('/delete_question/<email>/<int:question_number>', methods=['POST'])
def delete_question(email, question_number):
    cursor.execute("""
        DELETE FROM employee_questions
        WHERE email = %s AND question_number = %s
    """, (email, question_number))
    db.commit()

    # Renumber remaining questions
    cursor.execute("""
        SELECT question_number FROM employee_questions
        WHERE email = %s ORDER BY question_number
    """, (email,))
    all_questions = cursor.fetchall()

    for new_number, row in enumerate(all_questions, start=1):
        old_number = row[0]
        cursor.execute("""
            UPDATE employee_questions
            SET question_number = %s
            WHERE email = %s AND question_number = %s
        """, (new_number, email, old_number))

    db.commit()
    return redirect(f"/view_questions/{email}")
@app.route('/view_questions/<email>')
def view_questions(email):
    cursor.execute("""
        SELECT question_number, question_text, option_1, option_2, option_3, option_4
        FROM employee_questions
        WHERE email = %s
        ORDER BY question_number
    """, (email,))
    rows = cursor.fetchall()

    questions = [
        {
            'question_number': row[0],
            'question_text': row[1],
            'option_1': row[2],
            'option_2': row[3],
            'option_3': row[4],
            'option_4': row[5],
        }
        for row in rows
    ]

    return render_template('view_questions.html', questions=questions, email=email)
@app.route('/edit_question/<email>/<int:question_number>', methods=['POST'])
def edit_question(email, question_number):
    question_text = request.form['question_text']
    option_1 = request.form['option_1']
    option_2 = request.form['option_2']
    option_3 = request.form['option_3']
    option_4 = request.form['option_4']

    cursor.execute("""
        UPDATE employee_questions
        SET question_text = %s,
            option_1 = %s,
            option_2 = %s,
            option_3 = %s,
            option_4 = %s
        WHERE email = %s AND question_number = %s
    """, (question_text, option_1, option_2, option_3, option_4, email, question_number))
    db.commit()

    return redirect(f'/view_questions/{email}')

@app.route('/dashboard/<email>', methods=['GET', 'POST'])
def dashboard2k(email):
    # Do some logic here...
    print(f"Dashboard logic running for: {email}")

    # ✅ Redirect to another route
    return redirect(url_for('dashboard', email=email))


@app.route('/<email>', methods=['GET', 'POST'])
def dashboard(email):
    
    
    payment_conn = get_db_connection()
    payment_cur = payment_conn.cursor()

    try:
        payment_cur.execute(''' 
                            SELECT survey_url FROM payment_table WHERE contactEmail = %s''' , (email,)) 
        url_surveys = payment_cur.fetchone()
        url_survey = url_surveys[0] if url_surveys else " "
        
        payment_cur.execute(''' 
                            SELECT plan FROM payment_table WHERE contactEmail = %s''' , (email,))
        user_tiers = payment_cur.fetchone()
        user_tier = user_tiers[0] if user_tiers else " "
        # Fetch start and end dates directly from DB
        payment_cur.execute('''
            SELECT company_name, payment_status, surveyStartDate, surveyEndDate
            FROM payment_table WHERE contactEmail = %s
        ''', (email,))
        company_data = payment_cur.fetchone()

        if not company_data:
            return """
                <script>
                    alert("❌ Email not found.");
                    window.location.href = "/";
                </script>
            """, 404

        company_name, payment_status, start_date_raw, end_date_raw = company_data

        if payment_status != 'Payment Success':
            return """
                <script>
                    alert("❌ Access denied. This email id is payment pending.");
                    window.location.href = "/";
                </script>
            """, 403

        # Parse the dates
        today = datetime.now().date()
        if user_tier == 'Free Tier':
            start_date = datetime.strptime(str(start_date_raw), '%m/%d/%Y').date()
            end_date = datetime.strptime(str(end_date_raw), '%m/%d/%Y').date()
        else:
            start_date = datetime.strptime(str(start_date_raw), '%m/%d/%Y').date()
            end_date = datetime.strptime(str(end_date_raw), '%m/%d/%Y').date()

        if today < start_date:
            return "<script>alert('⏳ Your free trial has not started yet.'); window.history.back();</script>"

        if today > end_date:
            payment_cur.execute("""
                UPDATE payment_table 
                SET survey_url = NULL 
                WHERE contactEmail = %s
            """, (email,))
            payment_conn.commit()
            return '''
    <html>
    <body>
        <button onclick="goToDashboard('{{ location_data.email }}')">Check Trial Status</button>
      
     
        <script>
    // Show the button after 5 seconds and hide the overlay
    setTimeout(function() {
        document.getElementById('successOverlay').style.display = 'none';
        document.querySelector('.pay-button').style.display = 'inline-block';
    }, 5000);
    
    function goToDashboard(email) {
        const baseUrl = "https://dev-amazing-place-to-work.frandzzo.com/payment/";
        window.location.href = `${baseUrl}${email}`;
    }

    // ✅ Extract email from current URL and auto-assign
    function goToDashboardFromURL() {
        const currentUrl = window.location.href;
        const parts = currentUrl.split('/');
        const email = parts[parts.length - 1];
        goToDashboard(encodeURIComponent(email));
    }
</script>
        
    </body>
    </html>
    '''
    except mysql.connector.Error as err:
        print(f"Database error: {err}")
        return """
            <script>
                alert("❌ Database error occurred. Please try again later.");
                window.location.href = "/";
            </script>
        """, 500
    finally:
        try:
            payment_cur.close()
        except:
            pass
        payment_conn.close()

    # Proceed with user tier and dashboard logic...
    conn = get_db_connection()
    cur = conn.cursor()
    # Inside the POST handler in api_may7.py
    if 'profile_pic' in request.files:
        file = request.files['profile_pic']
        if file and allowed_file(file.filename):
            try:
                # Create unique filename
                timestamp = int(datetime.now().timestamp())
                safe_filename = secure_filename(file.filename)
                filename = f"{email}/{timestamp}_{safe_filename}"

                # Upload to S3
                s3_client.upload_fileobj(
                    file,
                    AWS_BUCKET_NAME,
                    f"profile_pics/{filename}",
                    ExtraArgs={
                        'ACL': 'public-read',
                        'ContentType': file.content_type
                    }
                )

                # Generate S3 URL
                file_url = f"https://{AWS_BUCKET_NAME}.s3.{AWS_REGION}.amazonaws.com/profile_pics/{filename}"

                # Update database
                update_query = "UPDATE companyscore SET profile_pics = %s WHERE survey_url = %s"
                cur.execute(update_query, (file_url, url_survey))
                conn.commit()

                return jsonify({
                    'success': True,
                    'message': 'Profile picture updated successfully',
                    'url': file_url
                })

            except ClientError as e:
                return jsonify({
                    'success': False,
                    'message': str(e)
                }), 500
            except Exception as e:
                return jsonify({
                    'success': False,
                    'message': str(e)
                }), 500

        return jsonify({
            'success': False,
            'message': 'Invalid file type or no file provided'
        }), 400
    if request.method == 'POST' and 'full_name' in request.form:
        # Handle profile information update
        full_name = request.form.get('full_name')
        contact = request.form.get('contact')
        full_name = full_name.split(' ')
        first_name = full_name[0]
        last_name = full_name[1]

        update_query = """
            UPDATE auth_user
            SET first_name = %s, last_name = %s
            WHERE email = %s
        """
        cur.execute(update_query, (first_name, last_name,  email))
        conn.commit()
        update_query2 = """
            UPDATE account_profile
            SET contact = %s
            WHERE workEmail = %s
        """
        cur.execute(update_query2, (contact, email))
        conn.commit()

        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify({
                'success': True,
                'message': 'Profile updated successfully',
                'redirect': url_for('dashboard', email=email, section='profile')
            })

        return redirect(url_for('dashboard2k', email=email))


    # Company name was already fetched in the payment check section

    # Fetch user details   
    query2 = """
    SELECT first_name, last_name, email
    FROM auth_user
    WHERE email = %s
    """
    cur.execute(query2, (email,))
    result = cur.fetchone()

    query3 = """
    SELECT contact,password
    FROM account_profile
    WHERE workEmail = %s
    """
    cur.execute(query3, (email,))
    result2 = cur.fetchone()

    query4 = """
    SELECT profile_pics
    FROM companyscore
    WHERE survey_url = %s
    """
    cur.execute(query4, (url_survey,))
    result3 = cur.fetchone()

    email = result[2] if result else None
    full_name = result[0] + ' ' + result[1] if result else None

    location_data = {}
    if result:
        location_data = {
            'full_name': result[0] + ' ' + result[1],
            'email': result[2],
            'contact': result2[0],
            'password': result2[1],
            'profile_pic' : result3[0] if result3 else None

        }


    # Home page company scores
    cur.execute('''SELECT dei_score, culture_engagement_score,
                   support_motivation_score, strategic_alignment_score,
                   skill_development_score FROM companyscore
                   WHERE survey_url = %s''', (url_survey,))
    home_page_scores = cur.fetchone()

    dei_score = home_page_scores[0] if home_page_scores else 0
    culture_score = home_page_scores[1] if home_page_scores else 0
    support_score = home_page_scores[2] if home_page_scores else 0
    strategy_score = home_page_scores[3] if home_page_scores else 0
    skill_score = home_page_scores[4] if home_page_scores else 0


    #Reputation Trsut page
    cur.execute('''SELECT credibility_score, fairness_score, team_spirit_score, well_being_score,
                   respect_score, workplace_satisfaction_score, open_communication_score,
                   recognition_score, motivation_score FROM companyscore
                   WHERE survey_url = %s''', (url_survey,))
    reputation_scores = cur.fetchone()

    credibility = round(reputation_scores[0] if reputation_scores else 0,0)
    fairness = round(reputation_scores[1] if reputation_scores else 0,0)
    teamspirit = round(reputation_scores[2] if reputation_scores else 0,0)
    wellbeing = round(reputation_scores[3] if reputation_scores else 0,0)
    respect = round(reputation_scores[4] if reputation_scores else 0,0)
    workplace = round(reputation_scores[5] if reputation_scores else 0,0)
    opencommunication = round(reputation_scores[6] if reputation_scores else 0,0)
    recognition = round(reputation_scores[7] if reputation_scores else 0,0)
    motivation = round(reputation_scores[8] if reputation_scores else 0,0)

    # Executive Summary Page 1
    # Strategic Alignment Report
    cur.execute('''SELECT strategic_alignment_positive_pct, strategic_alignment_neutral_pct, strategic_alignment_negative_pct,
                strategic_alignment_positive_male_pct, strategic_alignment_positive_female_pct, strategic_alignment_neutral_male_pct, 
                strategic_alignment_neutral_female_pct, strategic_alignment_negative_male_pct, strategic_alignment_negative_female_pct
                FROM companyscore
                   WHERE survey_url = %s''', (url_survey,))
    strategic_alignment_report = cur.fetchone()

    engaged = round(strategic_alignment_report[0] if strategic_alignment_report else 0,0)
    inBetween = round(strategic_alignment_report[1] if strategic_alignment_report else 0,0)
    disengaged = round(strategic_alignment_report[2] if strategic_alignment_report else 0,0)
    engaged_male = round(strategic_alignment_report[3] if strategic_alignment_report else 0,0)
    engaged_female = round(strategic_alignment_report[4] if strategic_alignment_report else 0,0)
    inBetween_male = round(strategic_alignment_report[5] if strategic_alignment_report else 0,0)
    inBetween_female = round(strategic_alignment_report[6] if strategic_alignment_report else 0,0)
    disengaged_male = round(strategic_alignment_report[7] if strategic_alignment_report else 0,0)
    disengaged_female = round(strategic_alignment_report[8] if strategic_alignment_report else 0,0)

    #Diversity, Equity, Inclusion Summary
    cur.execute('''SELECT diversity_score, equity_score, inclusion_score FROM companyscore
                   WHERE survey_url = %s''', (url_survey,))
    dei_summary = cur.fetchone()

    diversity = round(dei_summary[0] if dei_summary else 0,0)
    equity = round(dei_summary[1] if dei_summary else 0,0)
    inclusion = round(dei_summary[2] if dei_summary else 0,0)
    DEI = [dei_score, diversity, equity, inclusion]


    #LPW Report
    cur.execute('''SELECT leadership_positive_pct, leadership_neutral_pct, leadership_negative_pct,
                   policies_positive_pct, policies_neutral_pct, policies_negative_pct, workplace_culture_positive_pct,
                   workplace_culture_neutral_pct, workplace_culture_negative_pct FROM companyscore
                   WHERE survey_url = %s''', (url_survey,))
    lpw_report = cur.fetchone()
    leadership_positive = round(lpw_report[0] if lpw_report else 0,0)
    leadership_neutral = round(lpw_report[1]    if lpw_report else 0,0)
    leadership_negative = round(lpw_report[2]   if lpw_report else 0,0)
    policies_positive = round(lpw_report[3] if lpw_report else 0,0)
    policies_neutral = round(lpw_report[4] if lpw_report else 0,0)
    policies_negative = round(lpw_report[5] if lpw_report else 0,0)
    workplace_culture_positive = round(lpw_report[6] if lpw_report else 0,0)
    workplace_culture_neutral = round(lpw_report[7] if lpw_report else 0,0)
    workplace_culture_negative = round(lpw_report[8] if lpw_report else 0,0)

    leadership = [leadership_positive, leadership_neutral, leadership_negative]
    policy = [policies_positive, policies_neutral, policies_negative]
    work = [workplace_culture_positive, workplace_culture_neutral, workplace_culture_negative]


    # Key Metrics
    cur.execute('''SELECT proud_to_work_score, people_care_score, fair_promotion_score,
                   involvement_decision_score, leadership_reachable_score FROM companyscore
                   WHERE survey_url = %s''', (url_survey,))
    key_metric = cur.fetchone()
    key_mertics = [round(key_metric[0] if key_metric else 0,0), round(key_metric[1] if key_metric else 0,0), round(key_metric[2] if key_metric else 0,0), round(key_metric[3] if key_metric else 0,0), round(key_metric[4] if key_metric else 0,0)]


    # Fetch department metrics
    cur.execute('''SELECT dept_HR_and_Admin_positive_pct, dept_HR_and_Admin_neutral_pct, dept_HR_and_Admin_negative_pct,
                dept_Finance_and_accounting_positive_pct, dept_Finance_and_accounting_neutral_pct, dept_Finance_and_accounting_negative_pct,
                dept_Sales_marketing_positive_pct, dept_Sales_marketing_neutral_pct, dept_Sales_marketing_negative_pct,
                dept_Product_development_positive_pct, dept_Product_development_neutral_pct, dept_Product_development_negative_pct,
                dept_Technical_positive_pct, dept_Technical_neutral_pct, dept_Technical_negative_pct,dept_Operations_positive_pct, 
                dept_Operations_neutral_pct, dept_Operations_negative_pct, dept_Procurement_positive_pct, dept_Procurement_neutral_pct, 
                dept_Procurement_negative_pct,dept_Quality_positive_pct, dept_Quality_neutral_pct, dept_Quality_negative_pct, 
                dept_Business_Development_positive_pct, dept_Business_Development_neutral_pct, dept_Business_Development_negative_pct,
                dept_executive_positive_pct, dept_executive_neutral_pct, dept_executive_negative_pct, dept_leadership_positive_pct, 
                dept_leadership_neutral_pct, dept_leadership_negative_pct, dept_Management_positive_pct, dept_Management_neutral_pct, 
                dept_Management_negative_pct, dept_others_positive_pct, dept_others_neutral_pct, dept_others_negative_pct
                 FROM companyscore WHERE survey_url = %s''', (url_survey,))

    department_metrics = cur.fetchone()
    department = [ 'HR', 'Finance', 'Marketing', 'Product Development', 'Technical','Operations','Procurement','Quality','Business & Development','Executive','Leadership','Management','Others']
    green_percentage = [round(department_metrics[0] if department_metrics else 0,0), round(department_metrics[3] if department_metrics else 0,0), round(department_metrics[6] if department_metrics else 0,0), round(department_metrics[9] if department_metrics else 0,0), round(department_metrics[12] if department_metrics else 0,0), round(department_metrics[15] if department_metrics else 0,0),round(department_metrics[18] if department_metrics else 0,0),round(department_metrics[21] if department_metrics else 0,0),round(department_metrics[24] if department_metrics else 0,0),round(department_metrics[27] if department_metrics else 0,0),round(department_metrics[30] if department_metrics else 0,0),round(department_metrics[33] if department_metrics else 0,0),round(department_metrics[36] if department_metrics else 0,0)]
    yellow_percentage = [round(department_metrics[1] if department_metrics else 0,0), round(department_metrics[4] if department_metrics else 0,0), round(department_metrics[7] if department_metrics else 0,0), round(department_metrics[10] if department_metrics else 0,0), round(department_metrics[13] if department_metrics else 0,0),round(department_metrics[16] if department_metrics else 0,0),round(department_metrics[19] if department_metrics else 0,0),round(department_metrics[22] if department_metrics else 0,0),round(department_metrics[25] if department_metrics else 0,0),round(department_metrics[28] if department_metrics else 0,0),round(department_metrics[31] if department_metrics else 0,0),round(department_metrics[34] if department_metrics else 0,0),round(department_metrics[37] if department_metrics else 0,0)]
    red_percentage = [round(department_metrics[2] if department_metrics else 0,0), round(department_metrics[5] if department_metrics else 0,0), round(department_metrics[8] if department_metrics else 0,0), round(department_metrics[11] if department_metrics else 0,0), round(department_metrics[14] if department_metrics else 0,0),round(department_metrics[17] if department_metrics else 0,0),round(department_metrics[20] if department_metrics else 0,0),round(department_metrics[23] if department_metrics else 0,0),round(department_metrics[26] if department_metrics else 0,0),round(department_metrics[29] if department_metrics else 0,0),round(department_metrics[32] if department_metrics else 0,0),round(department_metrics[35] if department_metrics else 0,0),round(department_metrics[38] if department_metrics else 0,0)]

    departments = []
    for i, depar in enumerate(department):
        departments.append({
            'name': depar,
            'green_percentage': green_percentage[i] ,
            'yellow_percentage': yellow_percentage[i],
            'red_percentage': red_percentage[i]
        })



    # Fetch gender metrics
    cur.execute('SELECT gender_male_dei_score, gender_female_dei_score FROM companyscore WHERE survey_url = %s''', (url_survey,))
    gender_data = cur.fetchone()

    gender_datas = [
        ['male', round(gender_data[0] if gender_data else 0,0)],
        ['female', round(gender_data[1] if gender_data else 0,0)]
    ]

    # Create a DataFrame for gender data
    df = pd.DataFrame(gender_datas, columns=['gender', 'dei_score'])

    # Calculate gender percentages
    gender_stats = {
        'female_percentage': df[df['gender'] == 'female']['dei_score'].values[0],
        'male_percentage': df[df['gender'] == 'male']['dei_score'].values[0]
    }

    # Fetch experience metrics
    cur.execute('''SELECT exp_1to3_dei_score, exp_3to5_dei_score, exp_above5_dei_score, exp_30dto1_dei_score
                 FROM companyscore
                 WHERE survey_url = %s''', (url_survey,))
    experience_score = cur.fetchone()
    dei_scores = [round(experience_score[0] if experience_score else 0,0), round(experience_score[1] if experience_score else 0,0), round(experience_score[2] if experience_score else 0,0),round(experience_score[3] if experience_score else 0,0)]
    # Transform experience data into a list of dictionaries
    year_1_2 = dei_scores[0]
    year_3_5 = dei_scores[1]
    year_above_5 = dei_scores[2]
    year_30_1 = dei_scores[3]

        # Fetch experience metrics
    cur.execute('''SELECT exp_1to3_dei_score, exp_3to5_dei_score, exp_above5_dei_score, exp_30dto1_dei_score
                 FROM companyscore
                 WHERE survey_url = %s''', (url_survey,))
    experience_score = cur.fetchone()
    experience_data = ['30 days -1 Years','1-2 Years', '2-5 Years','5+ Years ']
    dei_scores = [round(experience_score[0] if experience_score else 0,0), round(experience_score[1] if experience_score else 0,0), round(experience_score[2] if experience_score else 0,0),round(experience_score[3] if experience_score else 0,0)]
    # Transform experience data into a list of dictionaries
    experience_stats = []
    colors = ['#76b7b2','#fca5a5', '#86efac', '#fde047']  # Example colors
    start_degree = 0
    for i, exp_data in enumerate(experience_data):
        experience = exp_data
        percentage = dei_scores[i]  # Use the corresponding dei_score for the experience range
        end_degree = start_degree + (percentage / 100 * 360)
        experience_stats.append({
            'range': experience,
            'percentage': percentage,
            'color': colors[i % len(colors)],  # Cycle through colors
            'start_degree': start_degree,
            'end_degree': end_degree,
            'dei_score': dei_scores
        })
        start_degree = end_degree
   

    # Example engagement levels
    cur.execute('''SELECT role_manager_engagement_score, role_executive_engagement_score,
                role_team_member_engagement_score,role_Senior_staff_engagement_score, role_Junior_staff_engagement_score
                 FROM companyscore
                 WHERE survey_url = %s''', (url_survey,))
    engagement_levels = cur.fetchone()
    levels = [
        {"name": "Manager ", "score": round(engagement_levels[0] if engagement_levels else 0,0) , "color": "#22c55e"},
        {"name": "Executive ", "score": round(engagement_levels[1] if engagement_levels else 0,0), "color": "#fde047"},
        {"name": "Team ", "score": round(engagement_levels[2] if engagement_levels else 0,0), "color": "#ef4444"},
        {"name": "Senior Staff ", "score": round(engagement_levels[3] if engagement_levels else 0,0) , "color": "#455998"},
        {"name": "Junior Staff ", "score": round(engagement_levels[4] if engagement_levels else 0,0), "color": "#CECA56"}
    ]



    cur.execute(" select survey_url from payment_table where contactEmail = %s", (email,))
    survey = cur.fetchone()
    survey_url = survey[0] if survey else None


    # NEW: Get survey end date from payment_table table
    cur.execute("SELECT surveyStartDate, surveyEndDate FROM payment_table WHERE contactEmail = %s", (email,))
    date_result = cur.fetchone()
    start_date = date_result[0] if date_result else None
    end_dates = date_result[1] if date_result else None
    if date_result:
        try:
            # Parse the date string (assuming format is day/month/year)
            end_date_str = date_result[1]
            if user_tier == 'Free Tier':
                end_date = datetime.strptime(end_date_str, "%m/%d/%Y")
            else:
                end_date = datetime.strptime(end_date_str, "%m/%d/%Y")
            end_date = end_date.replace(hour=23, minute=59, second=59)
        except ValueError:
            # Handle potential date format issues
            end_date = datetime.now()
    else:
        end_date = datetime.now()
        
    
 

    # Invoice Data
    cur.execute("SELECT invoice_id, invoice_date, customer_id, subscription_id, product_name, sub_total, gst_amount, grand_total FROM payment_table WHERE contactEmail = %s limit 1", (email,))
    invoice_data = cur.fetchone()
    invoice_id = invoice_data[0] if invoice_data else None
    invoice_date = invoice_data[1] if invoice_data else None
    customer_id = invoice_data[2] if invoice_data else None
    subscription_id = invoice_data[3] if invoice_data else None
    product_name = invoice_data[4] if invoice_data else None
    amount = invoice_data[5] if invoice_data else None
    igst = invoice_data[6] if invoice_data else None
    total = invoice_data[7] if invoice_data else None



    # Certfification
    cur.execute('''SELECT surveyEndDate,leaderName,product_name FROM payment_table WHERE survey_url = %s''', (url_survey,))
    certification = cur.fetchone()
    if certification and isinstance(certification[0], str):
        try:
            end_data = datetime.strptime(certification[0], "%m/%d/%Y")
            validity = end_data + timedelta(days=365)
            end_data_strs = end_data.strftime('%b %Y')
            validity_strs = validity.strftime('%b %Y')
        except ValueError:
            # Handle invalid date format gracefully
            end_data_strs = validity_strs = "Invalid Date"
    else:
        end_data_strs = validity_strs = "No Data"

    cur.execute("SELECT surveyEndDate,country FROM payment_table WHERE contactEmail = %s", (email,))
    date_ends = cur.fetchone()
    date_end = date_ends[0] if date_ends else None
    today = datetime.now().date()
    print(end_date)
    today = datetime.now().date()
    presnt_date = today.strftime("%m/%d/%Y")
    country = date_ends[1] if date_ends else None
    print(today)
    print(presnt_date)
    #if today == date_end:
    survey = "Allow the download"
        #print(survey)
    #else:
        #survey = 'Not allow download'

        
    

    
    
    certification_name = company_name
    
    certification_value = None

    visionary = certification[2] if certification else None

    if visionary == 'Best Visionary Leader Badge':
        leader_names = certification[1] if certification else None
    else: 
        leader_names = None

        
    plane_name = visionary



    # Engagement Rate and Response Rate
    cur.execute('''SELECT engagement_rate_score, total_responses, num_surveys FROM companyscore WHERE survey_url = %s''', (url_survey,))
    stats_data = cur.fetchone()
    engagement_rate = stats_data[0] if stats_data else 0
    response_count = stats_data[1] if stats_data else 0
    cur.execute('select num_surveys from payment_table where survey_url =%s',(url_survey,))
    team_counts = cur.fetchone()
    team_count = team_counts[0] if team_counts else 0
    

    status = {
        "engagementRate": round(engagement_rate,0),
        "responseRate": round(response_count / team_count * 100) if team_count else 0,
        "response_count": response_count,
        "team_count": team_count
    }
    cur.execute('''SELECT emp_voice1_positive_pct, emp_voice1_neutral_pct, emp_voice1_negative_pct,
                emp_voice2_positive_pct, emp_voice2_neutral_pct, emp_voice2_negative_pct,
                emp_voice3_positive_pct, emp_voice3_neutral_pct, emp_voice3_negative_pct,
                emp_voice4_positive_pct, emp_voice4_neutral_pct, emp_voice4_negative_pct,
                emp_voice5_positive_pct, emp_voice5_neutral_pct, emp_voice5_negative_pct,
                emp_voice6_positive_pct, emp_voice6_neutral_pct, emp_voice6_negative_pct,
                emp_voice7_positive_pct, emp_voice7_neutral_pct, emp_voice7_negative_pct,
                emp_voice8_positive_pct, emp_voice8_neutral_pct, emp_voice8_negative_pct,
                emp_voice9_positive_pct, emp_voice9_neutral_pct, emp_voice9_negative_pct,
                emp_voice10_positive_pct, emp_voice10_neutral_pct, emp_voice10_negative_pct,
                emp_voice11_positive_pct, emp_voice11_neutral_pct, emp_voice11_negative_pct,
                emp_voice12_positive_pct, emp_voice12_neutral_pct, emp_voice12_negative_pct,
                emp_voice13_positive_pct, emp_voice13_neutral_pct, emp_voice13_negative_pct
                FROM companyscore WHERE survey_url = %s''', (url_survey,))
    survey_results = cur.fetchone()

    SURVEY_RESULTS = [
    {"statement_id": 1, "text": "I feel safe to speak up without fear of negative consequences.", "engaged": round(survey_results[0] if survey_results else 0,0), "neutral": round(survey_results[1] if survey_results else 0,0), "disengaged": round(survey_results[2] if survey_results else 0,0)},
    {"statement_id": 2, "text": "Leaders actively listen and follow through on employee feedback.", "engaged": round(survey_results[3] if survey_results else 0,0), "neutral": round(survey_results[4] if survey_results else 0,0), "disengaged": round(survey_results[5] if survey_results else 0,0)},
    {"statement_id": 3, "text": "The company consistently lives by its stated values.", "engaged": round(survey_results[6] if survey_results else 0,0), "neutral": round(survey_results[7] if survey_results else 0,0), "disengaged": round(survey_results[8] if survey_results else 0,0)},
    {"statement_id": 4, "text": "Our internal culture aligns with what we promote externally.", "engaged": round(survey_results[9] if survey_results else 0,0), "neutral":    round(survey_results[10] if survey_results else 0,0), "disengaged": round(survey_results[11] if survey_results else 0,0)},
    {"statement_id": 5, "text": "Leadership communicates with transparency and honesty.", "engaged": round(survey_results[12] if survey_results else 0,0), "neutral": round(survey_results[13] if survey_results else 0,0), "disengaged": round(survey_results[14] if survey_results else 0,0)},
    {"statement_id": 6, "text": "I trust leaders to make fair and thoughtful decisions.", "engaged": round(survey_results[15] if survey_results else 0,0), "neutral": round(survey_results[16] if survey_results else 0,0), "disengaged": round(survey_results[17] if survey_results else 0,0)},
    {"statement_id": 7, "text": "I feel a genuine sense of belonging in the organization.", "engaged": round(survey_results[18] if survey_results else 0,0), "neutral": round(survey_results[19] if survey_results else 0,0), "disengaged": round(survey_results[20] if survey_results else 0,0)},
    {"statement_id": 8, "text": "My unique identity and contributions are respected and valued.", "engaged": round(survey_results[21] if survey_results else 0,0), "neutral": round(survey_results[22] if survey_results else 0,0), "disengaged": round(survey_results[23] if survey_results else 0,0)},
    {"statement_id": 9, "text": "My work is recognized, and I feel appreciated.", "engaged": round(survey_results[24] if survey_results else 0,0), "neutral": round(survey_results[25] if survey_results else 0,0), "disengaged": round(survey_results[26] if survey_results else 0,0)},
    {"statement_id": 10, "text": "I see clear opportunities for growth and development here.", "engaged": round(survey_results[27] if survey_results else 0,0), "neutral": round(survey_results[28] if survey_results else 0,0), "disengaged": round(survey_results[29] if survey_results else 0,0)},
    {"statement_id": 11, "text": "I have a healthy work-life balance and feel supported.", "engaged": round(survey_results[30] if survey_results else 0,0), "neutral": round(survey_results[31] if survey_results else 0,0), "disengaged": round(survey_results[32] if survey_results else 0,0)},
    {"statement_id": 12, "text": "The company genuinely cares about my mental and emotional well-being.", "engaged": round(survey_results[33] if survey_results else 0,0), "neutral": round(survey_results[34] if survey_results else 0,0), "disengaged": round(survey_results[35] if survey_results else 0,0)},
    {"statement_id": 13, "text": "Promotions and rewards are handled fairly and transparently.", "engaged": round(survey_results[36] if survey_results else 0,0), "neutral": round(survey_results[37] if survey_results else 0,0), "disengaged": round(survey_results[38] if survey_results else 0,0)}
    ]


    sorted_results = sorted(
    SURVEY_RESULTS,
    key=lambda x: (x['neutral'] + x['disengaged']),
    reverse=True
        )



    # Fetch action_score from DB
    cur.execute('''
        SELECT communication_positive_pct, leadership_positive_pct, worklife_balance_positive_pct,
            career_development_positive_pct, recognition_rewards_positive_pct, employee_engagement_positive_pct,
            workplace_environment_positive_pct, inclusion_diversity_positive_pct, compensation_transparency_positive_pct,
            feedback_mechanisms_positive_pct, organizational_transparency_positive_pct, manager_employee_relationship_positive_pct,
            psychological_safety_positive_pct, mission_values_alignment_positive_pct, innovation_creativity_positive_pct 
        FROM action_table_scores 
        WHERE company_name = %s
    ''', (company_name,))
    action_score = cur.fetchone()
  # fallback to zeros or something meaningful


    cur.execute('SELECT industry_type FROM companyscore WHERE survey_url =%s', (url_survey,))
    industry = cur.fetchone()
    benchmark = get_industry_benchmark_percentages()
    
    if not industry or industry[0] not in benchmark:
        industry_type = "default"  # fallback to a default benchmark category you define
    else:
        industry_type = industry[0]
    


    # Define categories configuration
    categories = [
        ("communications", 0, "communication", "communication_positive_pct", evaluate_communication, "HR & Leadership", "90% positive feedback on communication surveys", '2025-06-01', '2025-12-31'),
        ("leadership", 1, "leadership_effectiveness", "leadership_positive_pct", evaluate_leadership_effectiveness, "Leadership Team", "Maintain 90% positive leadership effectiveness score", '2025-05-20', '2025-11-30'),
        ("worklife", 2, "work_life_balance", "worklife_balance_positive_pct", evaluate_work_life_balance, "HR Department", "Increase employee satisfaction with work-life balance by 5%", '2025-06-01', '2025-12-31'),
        ("career", 3, "career_development", "career_development_positive_pct", evaluate_career_development, "HR & Managers", "Increase internal promotions by 20%", '2025-07-01', '2026-06-30'),
        ("recognition", 4, "recognition_rewards", "recognition_rewards_positive_pct", evaluate_recognition_rewards, "HR & Management", "Increase recognition frequency by 50%", '2025-08-01', '2026-01-31'),
        ("engagement", 5, "employee_engagement", "employee_engagement_positive_pct", evaluate_employee_engagement, "HR & Team Leaders", "Increase employee engagement score by 10%", '2025-06-15', '2025-12-31'),
        ("environment", 6, "workplace_environment", "workplace_environment_positive_pct", evaluate_workplace_environment, "All Employees", "Maintain high scores on workplace environment surveys", '2025-05-20', '2025-11-30'),
        ("diversity", 7, "inclusion_diversity", "inclusion_diversity_positive_pct", evaluate_inclusion_diversity, "D&I Committee", "Increase representation in leadership by 5%", '2025-09-01', '2026-03-31'),
        ("compensation", 8, "compensation_transparency", "compensation_transparency_positive_pct", evaluate_compensation_transparency, "HR & Finance", "Improve employee perception of compensation fairness by 15%", '2025-07-01', '2026-01-31'),
        ("feedback", 9, "feedback_mechanisms", "feedback_mechanisms_positive_pct", evaluate_feedback_mechanisms, "HR & Managers", "Increase feedback submission rate by 25%", '2025-06-01', '2025-12-31'),
        ("transparency", 10, "organizational_transparency", "organizational_transparency_positive_pct", evaluate_organizational_transparency, "Executives", "Maintain transparency rating above 85%", '2025-06-01', '2025-12-31'),
        ("relationship", 11, "manager_employee_relationship", "manager_employee_relationship_positive_pct", evaluate_manager_employee_relationship, "Managers", "Improve manager-employee trust by 10%", '2025-07-01', '2026-01-31'),
        ("safety", 12, "psychological_safety", "psychological_safety_positive_pct", evaluate_psychological_safety, "All Employees", "Maintain high psychological safety scores", '2025-05-20', '2025-11-30'),
        ("values", 13, "mission_values_alignment", "mission_values_alignment_positive_pct", evaluate_mission_values_alignment, "Leadership & HR", "Improve mission alignment awareness by 10%", '2025-08-01', '2026-02-28'),
        ("innovation", 14, "innovation_creativity", "innovation_creativity_positive_pct", evaluate_innovation_creativity, "Innovation Team", "Encourage 2+ innovation submissions per employee", '2025-06-15', '2025-12-31')
    ]

    # Generate dummy_data dictionary
    dummy_data = {}

    for key, idx, short_key, bench_key, eval_func, owner, target, start, end in categories:
        score = action_score[idx] if action_score else 0
        if score is None:
            score = 0
        dummy_data[key] = [{
            "trust_rating": round(score,0),
            "trust_level": eval_func(score),
            "benchmark": benchmark[industry_type][bench_key],
            "insights": get_insights_from_scores(industry_type, bench_key, round(score,0)),
            #"action_items": process_category_if_score(company_name, score, short_key)
        }]

    
    # Properly close resources
    try:
        # Ensure all results are consumed before closing
        if cur and not cur.with_rows:
            cur.fetchall()
        cur.close()
    except Exception as e:
        print(f"Error closing cursor: {e}")
    finally:
        conn.close()

    COUNTRY_DATA = {
        "IN": {"Flag Img": "https://flagcdn.com/w320/in.png", "Flag":'India Flag', "language_name": "English", "language_short": "en"},
        "US": {"Flag Img": "https://flagcdn.com/w320/us.png", "Flag":'USA Flag', "language_name": "English", "language_short": "en"},
        "UK": {"Flag Img": "https://flagcdn.com/w320/gb.png", "Flag": 'UK Flag', "language_name": "English", "language_short": "en"},
        "SE": {"Flag Img": "https://flagcdn.com/w320/se.png", "Flag": 'Sweden Flag', "language_name": "Swedish", "language_short": "sv"},
        "DE": {"Flag Img": "https://flagcdn.com/w320/de.png", "Flag": 'Germany Flag', "language_name": "German", "language_short": "de"},
        "CN": {"Flag Img": "https://flagcdn.com/w320/cn.png", "Flag": 'China Flag', "language_name": "Chinese", "language_short": "zh-TW"},
        "JP": {"Flag Img": "https://flagcdn.com/w320/jp.png", "Flag": 'Japan Flag', "language_name": "Japanese", "language_short": "ja"},
        "NL": {"Flag Img": "https://flagcdn.com/w320/nl.png", "Flag": 'Dutch Flag', "language_name": "Dutch", "language_short": "nl"},
        "AE": {"Flag Img": "https://flagcdn.com/w320/ae.png", "Flag": 'UAE Flag', "language_name": "Arabic", "language_short": "ar"},
        "IT": {"Flag Img": "https://flagcdn.com/w320/it.png", "Flag": 'Italian Flag', "language_name": "Italian", "language_short": "it"},
        "RU": {"Flag Img": "https://flagcdn.com/w320/rs.png", "Flag": 'Russian Flag', "language_name": "Russian", "language_short": "ru"},
        "KR": {"Flag Img": "https://flagcdn.com/w320/kr.png", "Flag": 'Korean Flag', "language_name": "Korean", "language_short": "ko"},
        "MY": {"Flag Img": "https://flagcdn.com/w320/my.png", "Flag": 'Malaysia Flag', "language_name": "Malay", "language_short": "ms"},
        "TH": {"Flag Img": "https://flagcdn.com/w320/th.png", "Flag": 'Thailand Flag', "language_name": "Thai", "language_short": "th"}
    }
    def get_country_name(ip):
        try:
            if ip.startswith(("127.", "192.168.", "10.")):
                return "IN"  # Default to India for local testing

            response = requests.get(f"https://ipapi.co/{ip}/json/", timeout=5)
            data = response.json()
            return data.get("country_code", "IN")
        except Exception:
            return "IN"  # Default to India if API fails
    user_ip = request.headers.get("X-Forwarded-For", request.remote_addr)
    country_name = get_country_name(user_ip)

    # Render the template with the fetched data
    return render_template(
        'Dash_aws_may22.html',
        statsData=status,
        dei_score=round(dei_score,0),
        culture_score=round(culture_score,0),
        support_score=round(support_score,0),
        strategy_score=round(strategy_score,0),
        skill_score=round(skill_score,0),
        departments=departments,
        gender_stats=gender_stats,
        company_name=company_name,
        experience_stats = experience_stats,
        year_1_2 = year_1_2,
        year_3_5 = year_3_5,
        year_above_5 =year_above_5,
        engagement_levels=levels,
        location_data=location_data,
        testLink=survey_url,
        engaged=engaged,
        inBetween= inBetween,
        disengaged=disengaged,
        engaged_male = engaged_male,
        engaged_female = engaged_female,
        inBetween_male = inBetween_male,
        inBetween_female = inBetween_female,
        disengaged_male = disengaged_male,
        disengaged_female = disengaged_female,
        DEI =DEI,
        year_30_1 = year_30_1,
        work = work,
        policy = policy,
        key_mertics=key_mertics,
        leadership = leadership,
        credibility = credibility,
        fairness=fairness,
        teamspirit = teamspirit,
        wellbeing = wellbeing,
        respect = respect,
        survey = survey,
        workplace = workplace,
        opencommunication = opencommunication,
        recognition = recognition,
        motivation = motivation,
        survey_url = survey_url,
        start_date=start_date,
        end_dates=end_dates,
        email = email,
        certification_name = certification_name if certification_value else " ",
        validity_strs = validity_strs,
        end_data_strs = end_data_strs,
        leader_name = leader_names if leader_names else None,
        plan_name = plane_name,
        invoice_data = invoice_data,
        invoice_id = invoice_id,
        invoice_date = invoice_date,
        customer_id = customer_id,
        subscription_id = subscription_id,
        product_name = product_name,
        amount = amount,
        igst = igst,
        tier=user_tier,
        total = total,
        full_name = full_name,
        status=status,
        country_name=country,
        survey_results=sorted_results,
        dummyData = dummy_data,
        flag_img=COUNTRY_DATA[country_name]["Flag Img"],
        flag_name=COUNTRY_DATA[country_name]["Flag"],
        language_name=COUNTRY_DATA[country_name]["language_name"],
        language_short=COUNTRY_DATA[country_name]["language_short"],
        end_date=end_date.isoformat()
    )



if __name__ == '__main__':
    app.run(debug=True)