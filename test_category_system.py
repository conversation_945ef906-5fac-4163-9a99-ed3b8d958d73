"""
Test Script for Automatic Category Prediction System

This script demonstrates the automatic category prediction functionality
and shows how it integrates with the survey system.
"""

from crewai_agent import predict_sentiment, predict_category, process_predicted_categories
from automatic_category_processor import process_survey_categories, generate_category_report
import mysql.connector

def test_category_prediction():
    """Test the category prediction functionality"""
    print("=" * 60)
    print("TESTING CATEGORY PREDICTION SYSTEM")
    print("=" * 60)
    
    # Test sentences representing different workplace scenarios
    test_sentences = [
        "I feel safe to speak up without fear of negative consequences.",
        "Leadership actively listens and follows through on employee feedback.",
        "The company consistently lives by its stated values.",
        "I feel a genuine sense of belonging in the organization.",
        "My work is recognized, and I feel appreciated.",
        "I see clear opportunities for growth and development here.",
        "I have a healthy work-life balance and feel supported.",
        "Promotions and rewards are handled fairly and transparently.",
        "There is favoritism in our team and it affects morale.",
        "Communication from management is unclear and inconsistent."
    ]
    
    print("\n🧪 Testing Category Prediction on Sample Sentences:")
    print("-" * 50)
    
    for i, sentence in enumerate(test_sentences, 1):
        print(f"\n{i}. Sentence: {sentence}")
        
        # Predict sentiment
        sentiment = predict_sentiment(sentence)
        print(f"   Sentiment: {sentiment}")
        
        # Predict categories
        raw_categories = predict_category(sentence)
        processed_categories = process_predicted_categories(raw_categories)
        
        print(f"   Raw Categories: {raw_categories}")
        print(f"   Processed Categories: {processed_categories}")
        print("-" * 50)

def test_database_integration():
    """Test database integration for category storage"""
    print("\n" + "=" * 60)
    print("TESTING DATABASE INTEGRATION")
    print("=" * 60)
    
    try:
        # Connect to database
        db = mysql.connector.connect(
            host="fz-website-registration.c9uy0agkwmgg.ap-south-1.rds.amazonaws.com",
            user="admin",
            password="master123",
            database="registration"
        )
        cursor = db.cursor()
        
        # Check if predicted_categories column exists
        cursor.execute("""
            SELECT COLUMN_NAME 
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = 'registration' 
            AND TABLE_NAME = 'student_data' 
            AND COLUMN_NAME = 'predicted_categories'
        """)
        
        if cursor.fetchone():
            print("✅ predicted_categories column exists in student_data table")
        else:
            print("❌ predicted_categories column NOT found in student_data table")
        
        # Check if category_analytics table exists
        cursor.execute("""
            SELECT TABLE_NAME 
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_SCHEMA = 'registration' 
            AND TABLE_NAME = 'category_analytics'
        """)
        
        if cursor.fetchone():
            print("✅ category_analytics table exists")
        else:
            print("❌ category_analytics table NOT found")
        
        # Check recent survey data with categories
        cursor.execute("""
            SELECT COUNT(*) as total_responses,
                   COUNT(predicted_categories) as responses_with_categories
            FROM student_data 
            WHERE predicted_categories IS NOT NULL 
            AND predicted_categories != ''
        """)
        
        result = cursor.fetchone()
        if result:
            print(f"📊 Survey responses with categories: {result[1]} out of {result[0]} total")
        
        cursor.close()
        db.close()
        
    except Exception as e:
        print(f"❌ Database connection error: {e}")

def test_category_analytics():
    """Test category analytics functionality"""
    print("\n" + "=" * 60)
    print("TESTING CATEGORY ANALYTICS")
    print("=" * 60)
    
    try:
        # Connect to database to find a test survey
        db = mysql.connector.connect(
            host="fz-website-registration.c9uy0agkwmgg.ap-south-1.rds.amazonaws.com",
            user="admin",
            password="master123",
            database="registration"
        )
        cursor = db.cursor()
        
        # Get a survey URL that has data
        cursor.execute("""
            SELECT DISTINCT form_url 
            FROM student_data 
            WHERE predicted_categories IS NOT NULL 
            AND predicted_categories != ''
            LIMIT 1
        """)
        
        result = cursor.fetchone()
        if result:
            test_survey_url = result[0]
            print(f"📋 Testing with survey URL: {test_survey_url}")
            
            # Generate category report
            report = generate_category_report(test_survey_url)
            
            print(f"\n📈 Category Analytics Summary:")
            print(f"   Total Categories: {report['summary']['total_categories']}")
            print(f"   High Performing: {report['summary']['high_performing_count']}")
            print(f"   Medium Performing: {report['summary']['medium_performing_count']}")
            print(f"   Low Performing: {report['summary']['low_performing_count']}")
            
            if report['top_positive_categories']:
                print(f"\n🏆 Top Positive Categories:")
                for cat in report['top_positive_categories'][:3]:
                    print(f"   - {cat['category']}: {cat['percentage']:.1f}% positive")
            
            if report['top_negative_categories']:
                print(f"\n⚠️ Top Negative Categories:")
                for cat in report['top_negative_categories'][:3]:
                    print(f"   - {cat['category']}: {cat['percentage']:.1f}% negative")
        else:
            print("❌ No survey data with categories found for testing")
        
        cursor.close()
        db.close()
        
    except Exception as e:
        print(f"❌ Error testing category analytics: {e}")

def show_system_overview():
    """Show an overview of the automatic category prediction system"""
    print("\n" + "=" * 60)
    print("AUTOMATIC CATEGORY PREDICTION SYSTEM OVERVIEW")
    print("=" * 60)
    
    overview = """
🎯 SYSTEM FEATURES:
   ✅ AI-powered category prediction for survey responses
   ✅ Automatic sentiment analysis integration
   ✅ Real-time category processing after survey submission
   ✅ Category analytics and insights generation
   ✅ API endpoints for category data access
   ✅ Database storage for category metrics

📊 SUPPORTED CATEGORIES:
   • Primary: diversity, equity, inclusion, leadership, workplace_culture, employee_engagement
   • Secondary: strategic_alignment, culture_engagement, support_motivation, skill_development
   • Analytics: credibility, fairness, workplace_satisfaction, team_spirit, well_being, respect
   • Action: communication, leadership_effectiveness, work_life_balance, career_development

🔄 WORKFLOW:
   1. User submits survey response
   2. AI predicts sentiment + categories automatically
   3. Data stored in student_data table with predicted_categories
   4. Category analytics calculated and stored in category_analytics table
   5. Insights available via API endpoints and reports

🌐 API ENDPOINTS:
   • /api/category-analytics/<survey_url> - Full category report
   • /api/category-insights/<survey_url> - Basic category insights

💾 DATABASE TABLES:
   • student_data: Added predicted_categories column
   • category_analytics: New table for category metrics
   • companyscore: Updated with traditional metrics
    """
    
    print(overview)

if __name__ == "__main__":
    # Run all tests
    show_system_overview()
    test_category_prediction()
    test_database_integration()
    test_category_analytics()
    
    print("\n" + "=" * 60)
    print("TESTING COMPLETED")
    print("=" * 60)
