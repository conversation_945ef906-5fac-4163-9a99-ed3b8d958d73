"""
Automatic Category Processor

This module handles automatic processing of AI-predicted categories
and updates the companyscore table accordingly.
"""

import mysql.connector
from mysql.connector import Error
import pandas as pd
from decimal import Decimal
from survey_metrics_calculator import calculate_all_metrics
from action_table_calculations import calculate_all_action_metrics

def get_db_connection():
    """Create database connection"""
    try:
        return mysql.connector.connect(
            host="fz-website-registration.c9uy0agkwmgg.ap-south-1.rds.amazonaws.com",
            user="admin",
            password="master123",
            database="registration"
        )
    except Error as e:
        print(f"Error connecting to database: {e}")
        raise

def get_survey_responses_with_categories(survey_url):
    """Get all survey responses for a specific survey URL including predicted categories"""
    connection = None
    cursor = None
    
    try:
        connection = get_db_connection()
        cursor = connection.cursor()
        
        cursor.execute("""
            SELECT question_number, question_text, selected_text, predicted_sentiment, 
                   predicted_categories, gender, age_group, tenure_group, role, department,
                   unique_id, form_url, reason
            FROM student_data 
            WHERE form_url = %s
        """, (survey_url,))
        
        columns = [desc[0] for desc in cursor.description]
        data = cursor.fetchall()
        
        if data:
            df = pd.DataFrame(data, columns=columns)
            return df
        else:
            return pd.DataFrame()
            
    except Error as e:
        print(f"Error fetching survey responses: {e}")
        return pd.DataFrame()
        
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

def calculate_category_analytics(survey_responses):
    """Calculate analytics for each predicted category"""
    category_analytics = {}
    
    if survey_responses.empty:
        return category_analytics
    
    # Get all unique categories from all responses
    all_categories = set()
    for _, row in survey_responses.iterrows():
        if pd.notna(row['predicted_categories']) and row['predicted_categories']:
            categories = [cat.strip() for cat in row['predicted_categories'].split(',')]
            all_categories.update(categories)
    
    # Calculate metrics for each category
    for category in all_categories:
        # Filter responses that belong to this category
        category_responses = survey_responses[
            survey_responses['predicted_categories'].str.contains(category, na=False)
        ]
        
        if not category_responses.empty:
            total_responses = len(category_responses)
            
            # Count sentiment distribution
            positive_count = len(category_responses[category_responses['predicted_sentiment'] == 'Positive'])
            neutral_count = len(category_responses[category_responses['predicted_sentiment'] == 'Neutral'])
            negative_count = len(category_responses[category_responses['predicted_sentiment'] == 'Negative'])
            
            # Calculate percentages
            positive_pct = (positive_count / total_responses) * 100 if total_responses > 0 else 0
            neutral_pct = (neutral_count / total_responses) * 100 if total_responses > 0 else 0
            negative_pct = (negative_count / total_responses) * 100 if total_responses > 0 else 0
            
            # Calculate category score (same formula as DEI score)
            category_score = (
                (Decimal(str(positive_pct)) * Decimal('1.0')) +
                (Decimal(str(neutral_pct)) * Decimal('0.5')) +
                (Decimal(str(negative_pct)) * Decimal('-0.1'))
            )
            
            category_analytics[category] = {
                'total_responses': total_responses,
                'positive_count': positive_count,
                'neutral_count': neutral_count,
                'negative_count': negative_count,
                'positive_percentage': round(positive_pct, 2),
                'neutral_percentage': round(neutral_pct, 2),
                'negative_percentage': round(negative_pct, 2),
                'category_score': float(category_score.quantize(Decimal('0.01')))
            }
    
    return category_analytics

def update_category_analytics_table(survey_url, category_analytics):
    """Update the category_analytics table with calculated metrics"""
    connection = None
    cursor = None
    
    try:
        connection = get_db_connection()
        cursor = connection.cursor()
        
        for category, metrics in category_analytics.items():
            cursor.execute("""
                INSERT INTO category_analytics (
                    survey_url, category_name, question_count, positive_responses,
                    neutral_responses, negative_responses, total_responses,
                    positive_percentage, neutral_percentage, negative_percentage,
                    category_score
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                ON DUPLICATE KEY UPDATE
                    question_count = VALUES(question_count),
                    positive_responses = VALUES(positive_responses),
                    neutral_responses = VALUES(neutral_responses),
                    negative_responses = VALUES(negative_responses),
                    total_responses = VALUES(total_responses),
                    positive_percentage = VALUES(positive_percentage),
                    neutral_percentage = VALUES(neutral_percentage),
                    negative_percentage = VALUES(negative_percentage),
                    category_score = VALUES(category_score),
                    last_updated = CURRENT_TIMESTAMP
            """, (
                survey_url, category, metrics['total_responses'],
                metrics['positive_count'], metrics['neutral_count'],
                metrics['negative_count'], metrics['total_responses'],
                metrics['positive_percentage'], metrics['neutral_percentage'],
                metrics['negative_percentage'], metrics['category_score']
            ))
        
        connection.commit()
        print(f"Updated category analytics for {len(category_analytics)} categories")
        return True
        
    except Error as e:
        print(f"Error updating category analytics: {e}")
        if connection:
            connection.rollback()
        return False
        
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

def process_survey_categories(survey_url):
    """
    Main function to process categories for a survey and update all relevant tables
    """
    print(f"Processing categories for survey: {survey_url}")
    
    # Get survey responses with categories
    survey_responses = get_survey_responses_with_categories(survey_url)
    
    if survey_responses.empty:
        print("No survey responses found")
        return False
    
    # Calculate category analytics
    category_analytics = calculate_category_analytics(survey_responses)
    
    if not category_analytics:
        print("No categories found in survey responses")
        return False
    
    # Update category analytics table
    success = update_category_analytics_table(survey_url, category_analytics)
    
    if success:
        print(f"Successfully processed {len(category_analytics)} categories")
        
        # Also update traditional metrics (existing functionality)
        try:
            all_metrics = calculate_all_metrics(survey_responses)
            action_metrics = calculate_all_action_metrics(survey_responses)
            print("Traditional metrics calculated successfully")
        except Exception as e:
            print(f"Error calculating traditional metrics: {e}")
    
    return success

def get_category_insights(survey_url):
    """Get insights about predicted categories for a survey"""
    connection = None
    cursor = None
    
    try:
        connection = get_db_connection()
        cursor = connection.cursor()
        
        cursor.execute("""
            SELECT category_name, total_responses, positive_percentage,
                   neutral_percentage, negative_percentage, category_score
            FROM category_analytics 
            WHERE survey_url = %s
            ORDER BY category_score DESC
        """, (survey_url,))
        
        results = cursor.fetchall()
        
        insights = []
        for row in results:
            insights.append({
                'category': row[0],
                'total_responses': row[1],
                'positive_pct': row[2],
                'neutral_pct': row[3],
                'negative_pct': row[4],
                'score': row[5]
            })
        
        return insights
        
    except Error as e:
        print(f"Error getting category insights: {e}")
        return []
        
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

def get_top_categories_by_sentiment(survey_url, sentiment_type='Positive', limit=5):
    """Get top categories by sentiment type"""
    connection = None
    cursor = None

    try:
        connection = get_db_connection()
        cursor = connection.cursor()

        if sentiment_type == 'Positive':
            order_column = 'positive_percentage'
        elif sentiment_type == 'Negative':
            order_column = 'negative_percentage'
        else:
            order_column = 'neutral_percentage'

        cursor.execute(f"""
            SELECT category_name, {order_column}, total_responses, category_score
            FROM category_analytics
            WHERE survey_url = %s
            ORDER BY {order_column} DESC
            LIMIT %s
        """, (survey_url, limit))

        results = cursor.fetchall()

        categories = []
        for row in results:
            categories.append({
                'category': row[0],
                'percentage': row[1],
                'total_responses': row[2],
                'score': row[3]
            })

        return categories

    except Error as e:
        print(f"Error getting top categories: {e}")
        return []

    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

def get_category_comparison(survey_url):
    """Get comparison of all categories with their performance"""
    connection = None
    cursor = None

    try:
        connection = get_db_connection()
        cursor = connection.cursor()

        cursor.execute("""
            SELECT category_name, positive_percentage, negative_percentage,
                   total_responses, category_score
            FROM category_analytics
            WHERE survey_url = %s
            ORDER BY category_score DESC
        """, (survey_url,))

        results = cursor.fetchall()

        comparison = {
            'high_performing': [],  # Score > 70
            'medium_performing': [],  # Score 40-70
            'low_performing': []  # Score < 40
        }

        for row in results:
            category_data = {
                'category': row[0],
                'positive_pct': row[1],
                'negative_pct': row[2],
                'total_responses': row[3],
                'score': row[4]
            }

            if row[4] > 70:
                comparison['high_performing'].append(category_data)
            elif row[4] >= 40:
                comparison['medium_performing'].append(category_data)
            else:
                comparison['low_performing'].append(category_data)

        return comparison

    except Error as e:
        print(f"Error getting category comparison: {e}")
        return {'high_performing': [], 'medium_performing': [], 'low_performing': []}

    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

def generate_category_report(survey_url):
    """Generate a comprehensive category analysis report"""
    insights = get_category_insights(survey_url)
    comparison = get_category_comparison(survey_url)
    top_positive = get_top_categories_by_sentiment(survey_url, 'Positive', 3)
    top_negative = get_top_categories_by_sentiment(survey_url, 'Negative', 3)

    report = {
        'summary': {
            'total_categories': len(insights),
            'high_performing_count': len(comparison['high_performing']),
            'medium_performing_count': len(comparison['medium_performing']),
            'low_performing_count': len(comparison['low_performing'])
        },
        'all_categories': insights,
        'performance_breakdown': comparison,
        'top_positive_categories': top_positive,
        'top_negative_categories': top_negative
    }

    return report

if __name__ == "__main__":
    # Test the processor
    test_survey_url = "/test/<EMAIL>"
    process_survey_categories(test_survey_url)

    # Test analytics
    report = generate_category_report(test_survey_url)
    print("Category Report Generated:", report)
