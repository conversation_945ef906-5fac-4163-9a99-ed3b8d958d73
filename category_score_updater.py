"""
Category Score Updater

This module uses AI to predict categories for survey questions and automatically
updates the companyscore table with calculated scores using existing formulas.
"""

import mysql.connector
import pandas as pd
from crewai_agent import predict_category
from survey_metrics_calculator import calculate_dei_score, calculate_category_sentiment_percentages

def get_db_connection():
    """Create database connection"""
    try:
        return mysql.connector.connect(
            host="fz-website-registration.c9uy0agkwmgg.ap-south-1.rds.amazonaws.com",
            user="admin",
            password="master123",
            database="registration"
        )
    except Exception as e:
        print(f"Error connecting to database: {e}")
        raise

def get_survey_responses(survey_url):
    """Get survey responses for a specific survey URL"""
    connection = None
    cursor = None
    
    try:
        connection = get_db_connection()
        cursor = connection.cursor()
        
        cursor.execute("""
            SELECT question_number, question_text, selected_text, predicted_sentiment, 
                   gender, age_group, tenure_group, role, department, unique_id, form_url, reason
            FROM student_data 
            WHERE form_url = %s
        """, (survey_url,))
        
        columns = [desc[0] for desc in cursor.description]
        data = cursor.fetchall()
        
        if data:
            df = pd.DataFrame(data, columns=columns)
            return df
        else:
            return pd.DataFrame()
            
    except Exception as e:
        print(f"Error fetching survey responses: {e}")
        return pd.DataFrame()
        
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

def predict_question_categories(survey_responses):
    """Use AI to predict categories for each unique question"""
    question_categories = {}
    
    # Get unique questions
    unique_questions = survey_responses[['question_number', 'question_text']].drop_duplicates()
    
    for _, row in unique_questions.iterrows():
        question_text = row['question_text']
        question_number = row['question_number']
        
        try:
            # Use AI to predict categories
            predicted_categories = predict_category(question_text)
            
            # Clean and split categories
            if isinstance(predicted_categories, str):
                categories = [cat.strip().lower() for cat in predicted_categories.split(',')]
            else:
                categories = ['employee_engagement']  # Default fallback
            
            question_categories[question_number] = categories
            print(f"Question {question_number}: {categories}")
            
        except Exception as e:
            print(f"Error predicting categories for question {question_number}: {e}")
            question_categories[question_number] = ['employee_engagement']  # Default fallback
    
    return question_categories

def calculate_category_scores(survey_responses, question_categories):
    """Calculate scores for each predicted category using existing formulas"""
    category_scores = {}
    
    # Get all unique categories
    all_categories = set()
    for categories in question_categories.values():
        all_categories.update(categories)
    
    for category in all_categories:
        # Find questions that belong to this category
        category_questions = [q_num for q_num, cats in question_categories.items() if category in cats]
        
        if category_questions:
            # Calculate sentiment percentages for this category
            positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
                survey_responses, category_questions
            )
            
            # Calculate score using existing formula (same as DEI score)
            score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)
            
            category_scores[category] = {
                'positive_pct': positive_pct,
                'neutral_pct': neutral_pct,
                'negative_pct': negative_pct,
                'score': score,
                'questions': category_questions
            }
            
            print(f"Category '{category}': {float(score):.2f} (Questions: {category_questions})")
    
    return category_scores

def update_companyscore_table(survey_url, category_scores):
    """Update companyscore table with calculated category scores"""
    connection = None
    cursor = None
    
    try:
        connection = get_db_connection()
        cursor = connection.cursor()
        
        # Map AI-predicted categories to companyscore columns
        category_column_mapping = {
            'diversity': 'diversity_score',
            'equity': 'equity_score', 
            'inclusion': 'inclusion_score',
            'leadership': 'leadership_score',
            'workplace_culture': 'policies_score',
            'employee_engagement': 'culture_engagement_score',
            'strategic_alignment': 'strategic_alignment_score',
            'support_motivation': 'support_motivation_score',
            'skill_development': 'skill_development_score',
            'credibility': 'credibility_score',
            'fairness': 'fairness_score',
            'workplace_satisfaction': 'workplace_satisfaction_score',
            'team_spirit': 'team_spirit_score',
            'well_being': 'well_being_score',
            'respect': 'respect_score',
            'open_communication': 'open_communication_score',
            'recognition': 'recognition_score',
            'motivation': 'motivation_score',
            # Action categories (if action_table_scores table exists)
            'communication': 'communication_score',
            'leadership_effectiveness': 'leadership_effectiveness_score',
            'work_life_balance': 'work_life_balance_score',
            'career_development': 'career_development_score',
            'recognition_rewards': 'recognition_rewards_score',
            'workplace_environment': 'workplace_environment_score',
            'psychological_safety': 'psychological_safety_score'
        }
        
        # Build update query for companyscore table
        update_fields = []
        update_values = []
        
        for category, scores in category_scores.items():
            if category in category_column_mapping:
                column_name = category_column_mapping[category]
                update_fields.append(f"{column_name} = %s")
                update_values.append(float(scores['score']))
        
        if update_fields:
            update_query = f"""
                UPDATE companyscore 
                SET {', '.join(update_fields)}
                WHERE survey_url = %s
            """
            update_values.append(survey_url)
            
            cursor.execute(update_query, update_values)
            connection.commit()
            
            print(f"Updated companyscore table with {len(update_fields)} category scores")
            return True
        else:
            print("No matching categories found for companyscore table")
            return False
            
    except Exception as e:
        print(f"Error updating companyscore table: {e}")
        if connection:
            connection.rollback()
        return False
        
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

def process_survey_with_ai_categories(survey_url):
    """
    Main function to process a survey using AI category prediction
    and update companyscore table with calculated scores
    """
    print(f"Processing survey with AI categories: {survey_url}")
    
    # Get survey responses
    survey_responses = get_survey_responses(survey_url)
    
    if survey_responses.empty:
        print("No survey responses found")
        return False
    
    # Use AI to predict categories for questions
    question_categories = predict_question_categories(survey_responses)
    
    # Calculate scores for each category using existing formulas
    category_scores = calculate_category_scores(survey_responses, question_categories)
    
    # Update companyscore table
    success = update_companyscore_table(survey_url, category_scores)
    
    if success:
        print(f"Successfully processed {len(category_scores)} categories and updated companyscore table")
    
    return success

if __name__ == "__main__":
    # Test with a sample survey URL
    test_survey_url = "/test/<EMAIL>"
    process_survey_with_ai_categories(test_survey_url)
