from flask import Flask, request, render_template, redirect ,session
import mysql.connector
from crewai_agent import predict_sentiment, explain_sentiment, predict_category, process_predicted_categories
from automatic_category_processor import process_survey_categories

app = Flask(__name__)
app.secret_key = '********************************************************'

import uuid
# MySQL connection
db = mysql.connector.connect(
    host="fz-website-registration.c9uy0agkwmgg.ap-south-1.rds.amazonaws.com",
    user="admin",
    password="master123",
    database="registration"
)
cursor = db.cursor()



@app.route('/test/<email>', methods=['GET', 'POST'])
def test(email):
    if request.method == 'POST':
        if 'gender' in request.form:
            # Save form data in session
            session['email'] = email
            session['gender'] = request.form['gender']
            session['age_group'] = request.form['age_group']
            session['tenure_group'] = request.form['tenure_group']
            session['role'] = request.form['role']
            session['department'] = request.form['department']
            session['unique_id'] = str(uuid.uuid4())

            return redirect(f'/test/{email}?step=questions')

        else:
            # Final submission – store questions + session data in student_data
            try:
                for key in request.form:
                    if key.startswith('question_') and not key.startswith('question_text_'):
                        question_number = int(key.split('_')[1])
                        selected_option = request.form[key]
                        
                        # Check if question_text exists in form
                        question_text_key = f'question_text_{question_number}'
                        if question_text_key not in request.form:
                            return f"Error: Missing question text for question {question_number}", 400
                        
                        question_text = request.form[question_text_key]
                        
                        # Combine question and answer for better sentiment analysis
                        combined_text = f"{question_text} {selected_option}"

                        # Perform AI sentiment analysis
                        print(f"Analyzing sentiment for: {combined_text}")
                        predicted_sentiment = predict_sentiment(combined_text)
                        reason = explain_sentiment(combined_text)

                        # Perform AI category prediction
                        print(f"Predicting categories for: {combined_text}")
                        raw_categories = predict_category(combined_text)
                        processed_categories = process_predicted_categories(raw_categories)
                        categories_str = ','.join(processed_categories)

                        print(f"Predicted Sentiment: {predicted_sentiment}")
                        print(f"Predicted Categories: {categories_str}")

                        cursor.execute("""
                            INSERT INTO student_data (
                                question_number, question_text, selected_text, form_url,
                                unique_id, gender, age_group, tenure_group, role, department,
                                predicted_sentiment, reason, predicted_categories
                            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                        """, (
                            question_number, question_text, selected_option, f"/test/{email}",
                            session['unique_id'], session['gender'], session['age_group'],
                            session['tenure_group'], session['role'], session['department'],
                            predicted_sentiment, reason, categories_str
                        ))

                db.commit()

                # Process categories and update analytics after successful submission
                try:
                    survey_url = f"/test/{email}"
                    print(f"Processing categories for survey: {survey_url}")
                    process_survey_categories(survey_url)
                    print("Category processing completed successfully")
                except Exception as e:
                    print(f"Error processing categories: {e}")
                    # Don't fail the submission if category processing fails

                session.clear()
                return "Thank you! Test submitted successfully."
                
            except Exception as e:
                db.rollback()
                return f"Error submitting survey: {str(e)}", 500

    # GET Requests
    if request.args.get('step') == 'questions':
        # Show the survey questions
        cursor.execute("""
            SELECT question_number, question_text, option_1, option_2, option_3, option_4
            FROM employee_questions
            WHERE email = %s
            ORDER BY question_number
        """, (email,))
        rows = cursor.fetchall()

        questions = [
            {
                'question_number': row[0],
                'question_text': row[1],
                'option_1': row[2],
                'option_2': row[3],
                'option_3': row[4],
                'option_4': row[5]
            }
            for row in rows
        ]
        return render_template('test.html', questions=questions)

    # Step 1: Show user detail form
    return render_template('test_user_info.html', email=email)


if __name__ == '__main__':
    app.run(debug=True,port = 8080)

