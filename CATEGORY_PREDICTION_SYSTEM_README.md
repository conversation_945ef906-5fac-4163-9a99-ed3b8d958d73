# Automatic Category Prediction System

## Overview

This system implements AI-powered automatic categorization of survey questions and responses. After each survey submission, the system automatically predicts which categories the questions belong to and stores the results in respective database tables for analytics and insights.

## 🎯 Key Features

✅ **AI-Powered Category Prediction**: Uses advanced LLM to automatically categorize survey responses  
✅ **Real-time Processing**: Categories are predicted and stored immediately after survey submission  
✅ **Comprehensive Category Coverage**: Supports 30+ workplace categories including DEI, engagement, leadership  
✅ **Analytics & Insights**: Generates detailed reports and metrics for each category  
✅ **API Integration**: RESTful endpoints for accessing category data  
✅ **Database Integration**: Seamless storage in existing database structure  

## 📊 Supported Categories

### Primary Categories
- **diversity**: Questions about representation, demographics, and diverse perspectives
- **equity**: Questions about fairness, equal opportunities, and unbiased treatment
- **inclusion**: Questions about belonging, acceptance, and feeling valued
- **leadership**: Questions about management effectiveness, leadership quality, and guidance
- **workplace_culture**: Questions about company values, environment, and cultural aspects
- **employee_engagement**: Questions about motivation, involvement, and connection to work

### Secondary Categories
- **strategic_alignment**: Questions about company direction, vision, and goal alignment
- **culture_engagement**: Questions about cultural participation and engagement
- **support_motivation**: Questions about support systems and motivational factors
- **skill_development**: Questions about learning, growth, and career development
- **credibility**: Questions about trust, reliability, and organizational credibility
- **fairness**: Questions about just treatment and equitable processes
- **workplace_satisfaction**: Questions about job satisfaction and work environment
- **team_spirit**: Questions about collaboration and team dynamics
- **well_being**: Questions about mental health, work-life balance, and employee wellness
- **respect**: Questions about mutual respect and professional treatment
- **open_communication**: Questions about transparency and communication channels
- **recognition**: Questions about acknowledgment and appreciation
- **motivation**: Questions about drive, inspiration, and work motivation

### Action Categories
- **communication**: Questions about internal communication effectiveness
- **leadership_effectiveness**: Questions about leadership performance and impact
- **work_life_balance**: Questions about balance between work and personal life
- **career_development**: Questions about professional growth opportunities
- **recognition_rewards**: Questions about reward systems and recognition programs
- **workplace_environment**: Questions about physical and cultural work environment
- **inclusion_diversity**: Questions combining inclusion and diversity aspects
- **compensation_transparency**: Questions about pay equity and transparency
- **feedback_mechanisms**: Questions about feedback systems and processes
- **organizational_transparency**: Questions about company openness and transparency
- **manager_employee_relationship**: Questions about manager-employee interactions
- **psychological_safety**: Questions about safety to express opinions and concerns
- **mission_values_alignment**: Questions about alignment with company mission and values
- **innovation_creativity**: Questions about creative thinking and innovation

## 🔄 System Workflow

1. **Survey Submission**: User submits survey response through the web interface
2. **AI Analysis**: System automatically runs sentiment analysis AND category prediction
3. **Data Storage**: Results stored in `student_data` table with `predicted_categories` column
4. **Analytics Processing**: Category analytics calculated and stored in `category_analytics` table
5. **Insights Generation**: Comprehensive reports and insights available via API endpoints

## 💾 Database Schema

### Modified Tables

#### student_data
- **Added Column**: `predicted_categories` (TEXT) - Stores comma-separated list of predicted categories

#### New Tables

#### category_analytics
```sql
CREATE TABLE category_analytics (
    id INT AUTO_INCREMENT PRIMARY KEY,
    survey_url VARCHAR(255) NOT NULL,
    category_name VARCHAR(100) NOT NULL,
    question_count INT DEFAULT 0,
    positive_responses INT DEFAULT 0,
    neutral_responses INT DEFAULT 0,
    negative_responses INT DEFAULT 0,
    total_responses INT DEFAULT 0,
    positive_percentage DECIMAL(5,2) DEFAULT 0.00,
    neutral_percentage DECIMAL(5,2) DEFAULT 0.00,
    negative_percentage DECIMAL(5,2) DEFAULT 0.00,
    category_score DECIMAL(5,2) DEFAULT 0.00,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_survey_url (survey_url),
    INDEX idx_category_name (category_name),
    UNIQUE KEY unique_survey_category (survey_url, category_name)
);
```

## 🌐 API Endpoints

### Get Category Analytics
```
GET /api/category-analytics/<survey_url>
```
Returns comprehensive category report including:
- Summary statistics
- Performance breakdown (high/medium/low performing categories)
- Top positive and negative categories
- Detailed metrics for all categories

### Get Category Insights
```
GET /api/category-insights/<survey_url>
```
Returns basic category insights with scores and percentages.

## 📁 File Structure

### Core Files
- `crewai_agent.py` - Enhanced with category prediction functions
- `automatic_category_processor.py` - Category processing and analytics engine
- `app.py` - Modified to include category prediction in survey submission
- `api.py` - Added category analytics API endpoints
- `database_migration.py` - Database schema updates
- `test_category_system.py` - Comprehensive testing script

### Key Functions

#### Category Prediction
- `predict_category(sentence)` - AI-powered category prediction
- `process_predicted_categories(raw_categories)` - Validates and cleans predicted categories

#### Analytics & Processing
- `process_survey_categories(survey_url)` - Main processing function
- `calculate_category_analytics(survey_responses)` - Calculates category metrics
- `generate_category_report(survey_url)` - Generates comprehensive reports

## 🚀 Usage Examples

### Testing the System
```bash
python test_category_system.py
```

### Running Database Migration
```bash
python database_migration.py
```

### API Usage
```bash
# Get category analytics for a survey
curl http://localhost:5000/api/category-analytics/<EMAIL>

# Get basic category insights
curl http://localhost:5000/api/category-insights/<EMAIL>
```

## 🔧 Configuration

The system uses the existing database configuration and LLM setup. No additional configuration required.

## 📈 Benefits

1. **Automated Insights**: No manual categorization needed
2. **Real-time Analytics**: Immediate category-based insights after survey submission
3. **Comprehensive Coverage**: 30+ categories covering all aspects of workplace feedback
4. **Scalable**: Handles any number of survey responses automatically
5. **API-Ready**: Easy integration with dashboards and reporting tools
6. **Accurate**: AI-powered categorization with validation and fallback mechanisms

## 🔮 Future Enhancements

- Machine learning model training on historical data
- Custom category definitions per organization
- Real-time dashboard for category trends
- Automated alerts for low-performing categories
- Integration with action planning systems

## 📞 Support

For questions or issues with the category prediction system, refer to the test script outputs or check the API endpoints for debugging information.
