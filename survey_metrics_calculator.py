"""
Survey Metrics Calculator

This module provides a comprehensive suite of functions to calculate various metrics
from company survey responses, including:
- DEI (Diversity, Equity, Inclusion) scores
- Department-wise metrics
- Experience-based metrics
- Role engagement metrics
- Employee voice metrics
"""

from decimal import Decimal
import json
from datetime import datetime, timedelta

# Constants for mapping form values to database fields
DEPARTMENT_MAPPING = {
    'HR' : 'HR_and_Admin',
    'Finance' : 'Finance_and_accounting',
    'Sales' : 'Sales_marketing',
    'Product' : 'Product_development',
    'Technical' : 'Technical',
    'Operations' : 'Operations',
    'Procurements' : 'Procurement',
    'Quality' : 'Quality',
    'Business' : 'Business_Development' ,
    'Executive': 'executive',
    'Leadership' : 'leadership',
    'Management' : 'Management',
    'Others' : 'Others'
}

EXPERIENCE_MAPPING = {
    '0-1 year': '30dto1',  # Group with 1-2 years
    '1-3 years': '1to3',
    '3-5 years': '3to5',
    '5+ years': 'above5', 
}

ROLE_MAPPING = {
    'Junior Staff' : 'Junior_staff',
    'Senior Staff' : 'Senior_staff',
    'Manager': 'manager',
    'Executive': 'executive',
    'Trainee' : 'trainee',
    'Team' : 'team_member'

}

Gender_Mapping = {
    'Male' : 'Male',
    'Female' : 'Female',
    'Other' : 'Others'
}




# Employee Voice Statements for AI Matching
EMPLOYEE_VOICE_STATEMENTS = {
    1: "I feel safe to speak up without fear of negative consequences.",
    2: "Leaders actively listen and follow through on employee feedback",
    3: "The company consistently lives by its stated values",
    4: "Our internal culture aligns with what we promote externally",
    5: "Leadership communicates with transparency and honesty",
    6: "I trust leaders to make fair and thoughtful decisions",
    7: "I feel a genuine sense of belonging in the organization",
    8: "My unique identity and contributions are respected and valued",
    9: "My work is recognized, and I feel appreciated",
    10: "I see clear opportunities for growth and development here",
    11: "I have a healthy work-life balance and feel supported",
    12: "The company genuinely cares about my mental and emotional well-being",
    13: "Promotions and rewards are handled fairly and transparently"
}

# Benchmarks for KPI sets
BENCHMARKS_1ST_KPI = {
    'credibility': 85,
    'fairness': 75,
    'workplace_satisfaction': 80,
    'team_spirit': 65,
    'well_being': 72,
    'respect': 67,
    'open_communication': 56
}

BENCHMARKS_2ND_KPI = {
    'recognition': 75,
    'motivation': 75
}

# Core calculation functions
def calculate_total_responses(survey_responses):
    """Calculate the total number of unique employees who responded to the survey."""
    return survey_responses['roll_no'].nunique()

def calculate_response_rate(total_responses, num_surveys):
    """Calculate the response rate as a percentage."""
    if num_surveys <= 0:
        return Decimal('0.00')
    
    response_rate = (Decimal(total_responses) / Decimal(num_surveys)) * 100
    return response_rate.quantize(Decimal('0.01'))

def calculate_sentiment_percentages(survey_responses):
    """Calculate overall sentiment percentages."""
    total_with_sentiment = survey_responses[survey_responses['predicted_sentiment'].notna()].shape[0]  
    
    if total_with_sentiment == 0:
        return Decimal('0.00'), Decimal('0.00'), Decimal('0.00')

    positive_count = survey_responses[survey_responses['predicted_sentiment'] == "Positive"].shape[0]  
    neutral_count = survey_responses[survey_responses['predicted_sentiment'] == "Neutral"].shape[0]  
    negative_count = survey_responses[survey_responses['predicted_sentiment'] == "Negative"].shape[0] 

    positive_pct = (Decimal(positive_count) / Decimal(total_with_sentiment)) * 100
    neutral_pct = (Decimal(neutral_count) / Decimal(total_with_sentiment)) * 100
    negative_pct = (Decimal(negative_count) / Decimal(total_with_sentiment)) * 100

    return (
        positive_pct.quantize(Decimal('0.01')),
        neutral_pct.quantize(Decimal('0.01')),
        negative_pct.quantize(Decimal('0.01'))
    )

def calculate_category_sentiment_percentages(survey_responses, question_numbers):
    """Calculate sentiment percentages for a specific category of questions."""
    category_responses = survey_responses[survey_responses['question_number'].isin(question_numbers)]
    return calculate_sentiment_percentages(category_responses)
    
def calculate_dei_score(positive_pct, neutral_pct, negative_pct):
    """Calculate DEI score based on sentiment percentages."""
    score = (
        (positive_pct * Decimal('1.0')) +
        (neutral_pct * Decimal('0.5')) +
        (negative_pct * Decimal('-0.1'))
    )
    return score.quantize(Decimal('0.01'))

# Department metrics
def calculate_department_metrics(survey_responses):
    """Calculate DEI metrics for each department."""
    metrics = {}

    for dept_form_value, dept_db_value in DEPARTMENT_MAPPING.items():
        filtered_responses = survey_responses[survey_responses['department'] == dept_form_value]  

        if filtered_responses.empty:
            metrics[f'dept_{dept_db_value}_dei_score'] = Decimal('0.00')
            metrics[f'dept_{dept_db_value}_positive_pct'] = Decimal('0.00')
            metrics[f'dept_{dept_db_value}_neutral_pct'] = Decimal('0.00')
            metrics[f'dept_{dept_db_value}_negative_pct'] = Decimal('0.00')
            continue

        positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
            filtered_responses,
            list(range(20))
        )

        dei_score = calculate_dei_score(positive_pct, neutral_pct, negative_pct) 

        metrics[f'dept_{dept_db_value}_dei_score'] = dei_score
        metrics[f'dept_{dept_db_value}_positive_pct'] = positive_pct
        metrics[f'dept_{dept_db_value}_neutral_pct'] = neutral_pct
        metrics[f'dept_{dept_db_value}_negative_pct'] = negative_pct

    return metrics

# Experience metrics
def calculate_experience_metrics(survey_responses):
    """Calculate DEI metrics for different experience levels."""
    metrics = {}

    for exp_form_value, exp_db_value in EXPERIENCE_MAPPING.items():
        filtered_responses = survey_responses[survey_responses['tenure'] == exp_form_value]

        if filtered_responses.empty:
            metrics[f'exp_{exp_db_value}_dei_score'] = Decimal('0.00')
            metrics[f'exp_{exp_db_value}_positive_pct'] = Decimal('0.00')
            metrics[f'exp_{exp_db_value}_neutral_pct'] = Decimal('0.00')
            metrics[f'exp_{exp_db_value}_negative_pct'] = Decimal('0.00')
            continue

        positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
            filtered_responses, list(range(20))
        )

        dei_score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)

        metrics[f'exp_{exp_db_value}_dei_score'] = dei_score
        metrics[f'exp_{exp_db_value}_positive_pct'] = positive_pct
        metrics[f'exp_{exp_db_value}_neutral_pct'] = neutral_pct
        metrics[f'exp_{exp_db_value}_negative_pct'] = negative_pct

    return metrics



# Employee voice metrics
def calculate_voice_metrics(survey_responses, question_numbers):
    """Calculate metrics for a voice statement."""
    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, question_numbers
    )
    score = neutral_pct + negative_pct
    return positive_pct, neutral_pct, negative_pct, score

def prioritize_voice_statements(voice_metrics):
    """Prioritize voice statements based on score."""
    sorted_statements = sorted(
        voice_metrics.items(),
        key=lambda x: x[1],
        reverse=True
    )
    priorities = {}
    for rank, (statement_num, _) in enumerate(sorted_statements, 1):
        priorities[statement_num] = rank
    return priorities



def calculate_diversity_metrics(survey_responses, question_numbers):
    """Calculate diversity-related metrics using AI-predicted question numbers."""
    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, question_numbers
    )
    score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)

    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score
    }

def calculate_equity_metrics(survey_responses, question_numbers):
    """Calculate equity-related metrics using AI-predicted question numbers."""
    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, question_numbers
    )
    score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)

    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score
    }

def calculate_inclusion_metrics(survey_responses, question_numbers):
    """Calculate inclusion-related metrics using AI-predicted question numbers."""
    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, question_numbers
    )
    score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)

    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score
    }

def calculate_leadership_metrics(survey_responses, question_numbers):
    """Calculate leadership metrics using AI-predicted question numbers."""
    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, question_numbers
    )
    score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)

    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score
    }

def calculate_policies_metrics(survey_responses, question_numbers):
    """Calculate policies metrics using AI-predicted question numbers."""
    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, question_numbers
    )
    score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)

    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score
    }

def calculate_workplace_culture_metrics(survey_responses, question_numbers):
    """Calculate workplace culture metrics using AI-predicted question numbers."""
    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, question_numbers
    )
    score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)

    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score
    }

def calculate_all_metrics(survey_responses, question_numbers_dict=None):
    """Calculate all metrics in a single pass and return them in a dictionary."""
    metrics = {}

    total_responses = calculate_total_responses(survey_responses)
    positive_pct, neutral_pct, negative_pct = calculate_sentiment_percentages(survey_responses)
    metrics['overall'] = {
        'total_responses': total_responses,
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'dei_score': calculate_dei_score(positive_pct, neutral_pct, negative_pct)
    }

    # If question numbers are provided, calculate category metrics
    if question_numbers_dict:
        metrics.update(calculate_all_category_metrics(survey_responses, question_numbers_dict))
        metrics['departments'] = calculate_department_metrics(survey_responses)
        metrics['experiences'] = calculate_experience_metrics(survey_responses)
        metrics['genders'] = calculate_gender_metrics(survey_responses)
        metrics['roles'] = calculate_role_engagement_metrics(survey_responses, question_numbers_dict.get('culture_engagement', []))

        # Voice metrics if available
        if 'voice_questions' in question_numbers_dict:
            metrics['voice'] = calculate_all_voice_metrics(survey_responses, question_numbers_dict['voice_questions'])

        # Strategic alignment gender metrics
        if 'strategic_alignment' in question_numbers_dict:
            metrics['strategic_alignment_gender'] = calculate_strategic_alignment_gender_metrics(survey_responses, question_numbers_dict['strategic_alignment'])

        # Engagement metrics
        metrics.update(calculate_engagement(survey_responses, question_numbers_dict))

    return metrics

def calculate_credibility_metrics(survey_responses, question_numbers):
    """Calculate credibility-related metrics using AI-predicted question numbers."""
    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, question_numbers
    )
    score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)

    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score,
        'benchmark': BENCHMARKS_1ST_KPI['credibility']
    }

def calculate_fairness_metrics(survey_responses, question_numbers):
    """Calculate fairness-related metrics using AI-predicted question numbers."""
    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, question_numbers
    )
    score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)

    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score,
        'benchmark': BENCHMARKS_1ST_KPI['fairness']
    }

def calculate_workplace_satisfaction_metrics(survey_responses, question_numbers):
    """Calculate workplace satisfaction metrics using AI-predicted question numbers."""
    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, question_numbers
    )
    score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)

    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score,
        'benchmark': BENCHMARKS_1ST_KPI['workplace_satisfaction']
    }

def calculate_team_spirit_metrics(survey_responses, question_numbers):
    """Calculate team spirit metrics using AI-predicted question numbers."""
    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, question_numbers
    )
    score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)

    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score,
        'benchmark': BENCHMARKS_1ST_KPI['team_spirit']
    }

def calculate_wellbeing_metrics(survey_responses, question_numbers):
    """Calculate well-being metrics using AI-predicted question numbers."""
    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, question_numbers
    )
    score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)

    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score,
        'benchmark': BENCHMARKS_1ST_KPI['well_being']
    }

def calculate_respect_metrics(survey_responses, question_numbers):
    """Calculate respect-related metrics using AI-predicted question numbers."""
    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, question_numbers
    )
    score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)

    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score,
        'benchmark': BENCHMARKS_1ST_KPI['respect']
    }

def calculate_communication_metrics(survey_responses, question_numbers):
    """Calculate open communication metrics using AI-predicted question numbers."""
    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, question_numbers
    )
    score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)

    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score,
        'benchmark': BENCHMARKS_1ST_KPI['open_communication']
    }

def calculate_recognition_metrics(survey_responses, question_numbers):
    """Calculate recognition metrics using AI-predicted question numbers."""
    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, question_numbers
    )
    score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)

    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score,
        'benchmark': BENCHMARKS_2ND_KPI['recognition']
    }

def calculate_motivation_metrics(survey_responses, question_numbers):
    """Calculate motivation metrics using AI-predicted question numbers."""
    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, question_numbers
    )
    score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)

    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score,
        'benchmark': BENCHMARKS_2ND_KPI['motivation']
    }

def calculate_engagement(survey_responses, question_numbers_dict):
    """Calculate all engagement-related metrics using AI-predicted question numbers."""
    metrics = {}

    # Culture of engagement
    if 'culture_engagement' in question_numbers_dict:
        positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
            survey_responses, question_numbers_dict['culture_engagement']
        )
        score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)
        metrics['culture_of_engagement'] = {
            'positive_pct': positive_pct,
            'neutral_pct': neutral_pct,
            'negative_pct': negative_pct,
            'score': score
        }

    # Strategic alignment
    if 'strategic_alignment' in question_numbers_dict:
        positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
            survey_responses, question_numbers_dict['strategic_alignment']
        )
        score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)
        metrics['strategic_alignment'] = {
            'positive_pct': positive_pct,
            'neutral_pct': neutral_pct,
            'negative_pct': negative_pct,
            'score': score
        }

    # Support and motivation
    if 'support_motivation' in question_numbers_dict:
        positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
            survey_responses, question_numbers_dict['support_motivation']
        )
        score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)
        metrics['support_and_motivation'] = {
            'positive_pct': positive_pct,
            'neutral_pct': neutral_pct,
            'negative_pct': negative_pct,
            'score': score
        }

    # Skill development
    if 'skill_development' in question_numbers_dict:
        positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
            survey_responses, question_numbers_dict['skill_development']
        )
        score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)
        metrics['skill_development'] = {
            'positive_pct': positive_pct,
            'neutral_pct': neutral_pct,
            'negative_pct': negative_pct,
            'score': score
        }

    # Engagement rate
    if 'employee_engagement' in question_numbers_dict:
        positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
            survey_responses, question_numbers_dict['employee_engagement']
        )
        score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)
        metrics['engagement_rate'] = {
            'positive_pct': positive_pct,
            'neutral_pct': neutral_pct,
            'negative_pct': negative_pct,
            'score': score
        }

    return metrics

def calculate_proud_to_work_metrics(survey_responses, question_numbers):
    """Calculate proud to work here metrics using AI-predicted question numbers."""
    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, question_numbers
    )
    score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)

    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score
    }

def calculate_people_care_metrics(survey_responses, question_numbers):
    """Calculate metrics for how the company cares about its people using AI-predicted question numbers."""
    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, question_numbers
    )
    score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)

    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score
    }

def calculate_fair_promotion_metrics(survey_responses, question_numbers):
    """Calculate metrics for fair promotion practices using AI-predicted question numbers."""
    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, question_numbers
    )
    score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)

    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score
    }

def calculate_involvement_decision_metrics(survey_responses, question_numbers):
    """Calculate metrics for employee involvement in decisions using AI-predicted question numbers."""
    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, question_numbers
    )
    score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)

    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score
    }

def calculate_leadership_reachable_metrics(survey_responses, question_numbers):
    """Calculate metrics for leadership reachability using AI-predicted question numbers."""
    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, question_numbers
    )
    score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)

    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score
    }

def calculate_role_engagement_metrics(survey_responses, question_numbers):
    """Calculate engagement metrics for different roles using AI-predicted question numbers."""
    metrics = {}

    for role_form_value, role_db_value in ROLE_MAPPING.items():
        filtered_responses = survey_responses[survey_responses['role'] == role_form_value]

        if filtered_responses.empty:
            metrics[f'role_{role_db_value}_engagement_score'] = Decimal('0.00')
            metrics[f'role_{role_db_value}_positive_pct'] = Decimal('0.00')
            metrics[f'role_{role_db_value}_neutral_pct'] = Decimal('0.00')
            metrics[f'role_{role_db_value}_negative_pct'] = Decimal('0.00')
            continue

        positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
            filtered_responses, question_numbers
        )

        engagement_score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)

        metrics[f'role_{role_db_value}_engagement_score'] = engagement_score
        metrics[f'role_{role_db_value}_positive_pct'] = positive_pct
        metrics[f'role_{role_db_value}_neutral_pct'] = neutral_pct
        metrics[f'role_{role_db_value}_negative_pct'] = negative_pct

    return metrics

def calculate_strategic_alignment_gender_metrics(survey_responses, question_numbers):
    """Calculate strategic alignment metrics broken down by gender using AI-predicted question numbers."""
    metrics = {}

    # Calculate for each gender
    for gender in ["Male", "Female", "Other"]:
        # Filter responses for this gender
        gender_responses = survey_responses[survey_responses['gender'] == gender]

        if gender_responses.empty:
            # Set default values if no responses for this gender
            gender_key = gender.lower()
            metrics[f'strategic_alignment_positive_{gender_key}_pct'] = Decimal('0.00')
            metrics[f'strategic_alignment_neutral_{gender_key}_pct'] = Decimal('0.00')
            metrics[f'strategic_alignment_negative_{gender_key}_pct'] = Decimal('0.00')
            continue

        # Calculate sentiment percentages for strategic alignment questions for this gender
        positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
            gender_responses, question_numbers
        )

        # Store the percentages with appropriate keys
        gender_key = gender.lower()
        metrics[f'strategic_alignment_positive_{gender_key}_pct'] = positive_pct
        metrics[f'strategic_alignment_neutral_{gender_key}_pct'] = neutral_pct
        metrics[f'strategic_alignment_negative_{gender_key}_pct'] = negative_pct

    return metrics

def predict_voice_questions_mapping(survey_responses):
    """Use AI to predict which questions belong to which voice statements."""
    from crewai_agent import predict_voice_statement

    voice_question_mapping = {}

    # Get unique questions from survey responses
    unique_questions = survey_responses[['question_number', 'question_text']].drop_duplicates()

    for _, row in unique_questions.iterrows():
        question_number = row['question_number']
        question_text = row['question_text']

        try:
            # Use AI to predict which voice statement this question relates to
            voice_statement_num = predict_voice_statement(question_text)

            if voice_statement_num > 0:  # Valid voice statement found
                if voice_statement_num not in voice_question_mapping:
                    voice_question_mapping[voice_statement_num] = []
                voice_question_mapping[voice_statement_num].append(question_number)

                print(f"Question {question_number} mapped to Voice Statement {voice_statement_num}")
            else:
                print(f"Question {question_number} could not be mapped to any voice statement")

        except Exception as e:
            print(f"Error predicting voice statement for question {question_number}: {e}")

    return voice_question_mapping

def calculate_all_voice_metrics(survey_responses, voice_question_dict=None):
    """Calculate metrics for all voice statements using AI-predicted question numbers."""

    # If no voice question mapping provided, use AI to predict it
    if voice_question_dict is None:
        voice_question_dict = predict_voice_questions_mapping(survey_responses)

    voice_metrics = {}
    scores = {}

    for statement_num, questions in voice_question_dict.items():
        if questions:  # Only process if there are questions for this voice statement
            metrics = calculate_voice_metrics(survey_responses, questions)
            voice_metrics[f'voice{statement_num}'] = metrics
            scores[statement_num] = metrics[3]  # Voice score (neutral + negative percentages)

    # Prioritize voice statements based on scores (higher score = more priority)
    priorities = prioritize_voice_statements(scores)

    # Return metrics with priorities
    result = {}
    for key, metrics in voice_metrics.items():
        statement_num = int(key[5:])  # Extract statement number from 'voice{num}'
        result[key] = (*metrics, priorities.get(statement_num, 0))

    return result

def calculate_all_complex_metrics(survey_responses, question_numbers_dict):
    """Calculate all complex metrics using AI-predicted question numbers."""
    dept_metrics = calculate_department_metrics(survey_responses)
    experience_metrics = calculate_experience_metrics(survey_responses)
    gender_metrics = calculate_gender_metrics(survey_responses)

    # Role metrics need question numbers for engagement calculation
    role_metrics = {}
    if 'culture_engagement' in question_numbers_dict:
        role_metrics = calculate_role_engagement_metrics(survey_responses, question_numbers_dict['culture_engagement'])

    # Voice metrics need voice question mapping
    voice_metrics = {}
    if 'voice_questions' in question_numbers_dict:
        voice_metrics = calculate_all_voice_metrics(survey_responses, question_numbers_dict['voice_questions'])

    all_metrics = {}
    all_metrics.update(dept_metrics)
    all_metrics.update(experience_metrics)
    all_metrics.update(gender_metrics)
    all_metrics.update(role_metrics)
    all_metrics.update(voice_metrics)

    return all_metrics

def calculate_all_category_metrics(survey_responses, question_numbers_dict):
    """Calculate metrics for all question categories using AI-predicted question numbers."""
    metrics = {}

    # Main DEI categories
    if 'diversity' in question_numbers_dict:
        metrics['diversity'] = calculate_diversity_metrics(survey_responses, question_numbers_dict['diversity'])
    if 'equity' in question_numbers_dict:
        metrics['equity'] = calculate_equity_metrics(survey_responses, question_numbers_dict['equity'])
    if 'inclusion' in question_numbers_dict:
        metrics['inclusion'] = calculate_inclusion_metrics(survey_responses, question_numbers_dict['inclusion'])

    # Leadership, Policy, Workplace Culture
    if 'leadership' in question_numbers_dict:
        metrics['leadership'] = calculate_leadership_metrics(survey_responses, question_numbers_dict['leadership'])
    if 'workplace_culture' in question_numbers_dict:
        metrics['policies'] = calculate_policies_metrics(survey_responses, question_numbers_dict['workplace_culture'])
        metrics['workplace_culture'] = calculate_workplace_culture_metrics(survey_responses, question_numbers_dict['workplace_culture'])

    # Trust and reputation metrics
    if 'credibility' in question_numbers_dict:
        metrics['credibility'] = calculate_credibility_metrics(survey_responses, question_numbers_dict['credibility'])
    if 'fairness' in question_numbers_dict:
        metrics['fairness'] = calculate_fairness_metrics(survey_responses, question_numbers_dict['fairness'])
    if 'workplace_satisfaction' in question_numbers_dict:
        metrics['workplace_satisfaction'] = calculate_workplace_satisfaction_metrics(survey_responses, question_numbers_dict['workplace_satisfaction'])
    if 'team_spirit' in question_numbers_dict:
        metrics['team_spirit'] = calculate_team_spirit_metrics(survey_responses, question_numbers_dict['team_spirit'])
    if 'well_being' in question_numbers_dict:
        metrics['well_being'] = calculate_wellbeing_metrics(survey_responses, question_numbers_dict['well_being'])
    if 'respect' in question_numbers_dict:
        metrics['respect'] = calculate_respect_metrics(survey_responses, question_numbers_dict['respect'])
    if 'open_communication' in question_numbers_dict:
        metrics['open_communication'] = calculate_communication_metrics(survey_responses, question_numbers_dict['open_communication'])
    if 'recognition' in question_numbers_dict:
        metrics['recognition'] = calculate_recognition_metrics(survey_responses, question_numbers_dict['recognition'])
    if 'motivation' in question_numbers_dict:
        metrics['motivation'] = calculate_motivation_metrics(survey_responses, question_numbers_dict['motivation'])

    # Key metrics
    if 'proud_to_work' in question_numbers_dict:
        metrics['proud_to_work'] = calculate_proud_to_work_metrics(survey_responses, question_numbers_dict['proud_to_work'])
    if 'people_care' in question_numbers_dict:
        metrics['people_care'] = calculate_people_care_metrics(survey_responses, question_numbers_dict['people_care'])
    if 'fair_promotion' in question_numbers_dict:
        metrics['fair_promotion'] = calculate_fair_promotion_metrics(survey_responses, question_numbers_dict['fair_promotion'])
    if 'involvement_decision' in question_numbers_dict:
        metrics['involvement_decision'] = calculate_involvement_decision_metrics(survey_responses, question_numbers_dict['involvement_decision'])
    if 'leadership_reachable' in question_numbers_dict:
        metrics['leadership_reachable'] = calculate_leadership_reachable_metrics(survey_responses, question_numbers_dict['leadership_reachable'])

    return metrics

def calculate_gender_metrics(survey_responses):
    """Calculate DEI metrics for different gender categories."""
    metrics = {}

    for gender_form_value, gender_db_value in Gender_Mapping.items():
        filtered_responses = survey_responses[survey_responses['gender'] == gender_form_value]

        if filtered_responses.empty:
            metrics[f'gender_{gender_db_value.lower()}_dei_score'] = Decimal('0.00')
            metrics[f'gender_{gender_db_value.lower()}_positive_pct'] = Decimal('0.00')
            metrics[f'gender_{gender_db_value.lower()}_neutral_pct'] = Decimal('0.00')
            metrics[f'gender_{gender_db_value.lower()}_negative_pct'] = Decimal('0.00')
            continue

        positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
            filtered_responses, list(range(20))
        )

        dei_score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)

        metrics[f'gender_{gender_db_value.lower()}_dei_score'] = dei_score
        metrics[f'gender_{gender_db_value.lower()}_positive_pct'] = positive_pct
        metrics[f'gender_{gender_db_value.lower()}_neutral_pct'] = neutral_pct
        metrics[f'gender_{gender_db_value.lower()}_negative_pct'] = negative_pct

    return metrics



def update_company_scores(company_scores_model, survey_response_model, payment_model):
    """Update company scores based on survey responses."""
    successful_payments = payment_model.objects.filter(payment_status='success')
    updated_count = 0

    for pay in successful_payments:
        survey_responses = survey_response_model.objects.filter(survey_url=pay.survey_url)

        if not survey_responses.exists():
            continue

        all_metrics = calculate_all_metrics(survey_responses)

        for category, category_metrics in all_metrics.items():
            for key, value in category_metrics.items():
                setattr(company_scores_model, f'{category}_{key}', value)

        company_scores_model.save()
        updated_count += 1

    return updated_count









def calculate_live_survey_timer(start_date, end_date):
    """Calculate the remaining time for the survey in HH:MM:SS format.
    Args:
        start_date: Survey start date as datetime object
        end_date: Survey end date as datetime object
    Returns:
        String in format 'HH:MM:SS' representing remaining time,
        or '00:00:00' if survey has ended or dates are invalid
    """
    try:
        if not start_date or not end_date:
            return '00:00:00'
            
        now = datetime.now()
        
        # If survey hasn't started yet or has ended
        if now < start_date or now > end_date:
            return '00:00:00'
        
        # Calculate remaining time
        time_remaining = end_date - now
        
        # Convert to hours, minutes, seconds
        total_seconds = int(time_remaining.total_seconds())
        hours = total_seconds // 3600
        minutes = (total_seconds % 3600) // 60
        seconds = total_seconds % 60
        
        return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
    except Exception as e:
        print(f"Error calculating timer: {str(e)}")
        return '00:00:00'

