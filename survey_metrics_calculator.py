"""
Survey Metrics Calculator

This module provides a comprehensive suite of functions to calculate various metrics
from company survey responses, including:
- DEI (Diversity, Equity, Inclusion) scores
- Department-wise metrics
- Experience-based metrics
- Role engagement metrics
- Employee voice metrics
"""

from decimal import Decimal
import json
from datetime import datetime, timedelta

# Constants for mapping form values to database fields
DEPARTMENT_MAPPING = {
    'HR' : 'HR_and_Admin',
    'Finance' : 'Finance_and_accounting',
    'Sales' : 'Sales_marketing',
    'Product' : 'Product_development',
    'Technical' : 'Technical',
    'Operations' : 'Operations',
    'Procurements' : 'Procurement',
    'Quality' : 'Quality',
    'Business' : 'Business_Development' ,
    'Executive': 'executive',
    'Leadership' : 'leadership',
    'Management' : 'Management',
    'Others' : 'Others'
}

EXPERIENCE_MAPPING = {
    '0-1 year': '30dto1',  # Group with 1-2 years
    '1-3 years': '1to3',
    '3-5 years': '3to5',
    '5+ years': 'above5', 
}

ROLE_MAPPING = {
    'Junior Staff' : 'Junior_staff',
    'Senior Staff' : 'Senior_staff',
    'Manager': 'manager',
    'Executive': 'executive',
    'Trainee' : 'trainee',
    'Team' : 'team_member'

}

Gender_Mapping = {
    'Male' : 'Male',
    'Female' : 'Female',
    'Other' : 'Others'
}




# Employee Voice Statement Questions
EMP_VOICE_QUESTIONS = {
    1: [14],      # "I feel safe to speak up without fear of negative consequences."
    2: [8],       # "Leaders actively listen and follow through on employee feedback"
    3: [11],      # "The company consistently lives by its stated values"
    4: [20],      # "Our internal culture aligns with what we promote externally"
    5: [11],      # "Leadership communicates with transparency and honesty"
    6: [15],      # "I trust leaders to make fair and thoughtful decisions"
    7: [3],       # "I feel a genuine sense of belonging in the organization"
    8: [2, 9],    # "My unique identity and contributions are respected and valued"
    9: [10],      # "My work is recognized, and I feel appreciated"
    10: [4],      # "I see clear opportunities for growth and development here"
    11: [7, 9],   # "I have a healthy work-life balance and feel supported"
    12: [7, 17],  # "The company genuinely cares about my mental and emotional well-being"
    13: [1, 5]    # "Promotions and rewards are handled fairly and transparently"
}

# Benchmarks for KPI sets
BENCHMARKS_1ST_KPI = {
    'credibility': 85,
    'fairness': 75,
    'workplace_satisfaction': 80,
    'team_spirit': 65,
    'well_being': 72,
    'respect': 67,
    'open_communication': 56
}

BENCHMARKS_2ND_KPI = {
    'recognition': 75,
    'motivation': 75
}

# Core calculation functions
def calculate_total_responses(survey_responses):
    """Calculate the total number of unique employees who responded to the survey."""
    return survey_responses['roll_no'].nunique()

def calculate_response_rate(total_responses, num_surveys):
    """Calculate the response rate as a percentage."""
    if num_surveys <= 0:
        return Decimal('0.00')
    
    response_rate = (Decimal(total_responses) / Decimal(num_surveys)) * 100
    return response_rate.quantize(Decimal('0.01'))

def calculate_sentiment_percentages(survey_responses):
    """Calculate overall sentiment percentages."""
    total_with_sentiment = survey_responses[survey_responses['predicted_sentiment'].notna()].shape[0]  
    
    if total_with_sentiment == 0:
        return Decimal('0.00'), Decimal('0.00'), Decimal('0.00')

    positive_count = survey_responses[survey_responses['predicted_sentiment'] == "Positive"].shape[0]  
    neutral_count = survey_responses[survey_responses['predicted_sentiment'] == "Neutral"].shape[0]  
    negative_count = survey_responses[survey_responses['predicted_sentiment'] == "Negative"].shape[0] 

    positive_pct = (Decimal(positive_count) / Decimal(total_with_sentiment)) * 100
    neutral_pct = (Decimal(neutral_count) / Decimal(total_with_sentiment)) * 100
    negative_pct = (Decimal(negative_count) / Decimal(total_with_sentiment)) * 100

    return (
        positive_pct.quantize(Decimal('0.01')),
        neutral_pct.quantize(Decimal('0.01')),
        negative_pct.quantize(Decimal('0.01'))
    )

def calculate_category_sentiment_percentages(survey_responses, question_numbers):
    """Calculate sentiment percentages for a specific category of questions."""
    category_responses = survey_responses[survey_responses['question_number'].isin(question_numbers)]
    return calculate_sentiment_percentages(category_responses)
    
def calculate_dei_score(positive_pct, neutral_pct, negative_pct):
    """Calculate DEI score based on sentiment percentages."""
    score = (
        (positive_pct * Decimal('1.0')) +
        (neutral_pct * Decimal('0.5')) +
        (negative_pct * Decimal('-0.1'))
    )
    return score.quantize(Decimal('0.01'))

# Department metrics
def calculate_department_metrics(survey_responses):
    """Calculate DEI metrics for each department."""
    metrics = {}

    for dept_form_value, dept_db_value in DEPARTMENT_MAPPING.items():
        filtered_responses = survey_responses[survey_responses['department'] == dept_form_value]  

        if filtered_responses.empty:
            metrics[f'dept_{dept_db_value}_dei_score'] = Decimal('0.00')
            metrics[f'dept_{dept_db_value}_positive_pct'] = Decimal('0.00')
            metrics[f'dept_{dept_db_value}_neutral_pct'] = Decimal('0.00')
            metrics[f'dept_{dept_db_value}_negative_pct'] = Decimal('0.00')
            continue

        positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
            filtered_responses,
            list(range(20))
        )

        dei_score = calculate_dei_score(positive_pct, neutral_pct, negative_pct) 

        metrics[f'dept_{dept_db_value}_dei_score'] = dei_score
        metrics[f'dept_{dept_db_value}_positive_pct'] = positive_pct
        metrics[f'dept_{dept_db_value}_neutral_pct'] = neutral_pct
        metrics[f'dept_{dept_db_value}_negative_pct'] = negative_pct

    return metrics

# Experience metrics
def calculate_experience_metrics(survey_responses):
    """Calculate DEI metrics for different experience levels."""
    metrics = {}

    for exp_form_value, exp_db_value in EXPERIENCE_MAPPING.items():
        filtered_responses = survey_responses[survey_responses['tenure'] == exp_form_value]

        if filtered_responses.empty:
            metrics[f'exp_{exp_db_value}_dei_score'] = Decimal('0.00')
            metrics[f'exp_{exp_db_value}_positive_pct'] = Decimal('0.00')
            metrics[f'exp_{exp_db_value}_neutral_pct'] = Decimal('0.00')
            metrics[f'exp_{exp_db_value}_negative_pct'] = Decimal('0.00')
            continue

        positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
            filtered_responses, list(range(20))
        )

        dei_score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)

        metrics[f'exp_{exp_db_value}_dei_score'] = dei_score
        metrics[f'exp_{exp_db_value}_positive_pct'] = positive_pct
        metrics[f'exp_{exp_db_value}_neutral_pct'] = neutral_pct
        metrics[f'exp_{exp_db_value}_negative_pct'] = negative_pct

    return metrics



# Employee voice metrics
def calculate_voice_metrics(survey_responses, question_numbers):
    """Calculate metrics for a voice statement."""
    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, question_numbers
    )
    score = neutral_pct + negative_pct
    return positive_pct, neutral_pct, negative_pct, score

def prioritize_voice_statements(voice_metrics):
    """Prioritize voice statements based on score."""
    sorted_statements = sorted(
        voice_metrics.items(),
        key=lambda x: x[1],
        reverse=True
    )
    priorities = {}
    for rank, (statement_num, _) in enumerate(sorted_statements, 1):
        priorities[statement_num] = rank
    return priorities



def calculate_all_metrics(survey_responses):
    """Calculate all metrics in a single pass and return them in a dictionary."""
    metrics = {}

    total_responses = calculate_total_responses(survey_responses)
    positive_pct, neutral_pct, negative_pct = calculate_sentiment_percentages(survey_responses)
    metrics['overall'] = {
        'total_responses': total_responses,
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'dei_score': calculate_dei_score(positive_pct, neutral_pct, negative_pct)
    }

    return metrics

def calculate_gender_metrics(survey_responses):
    """Calculate DEI metrics for different gender categories."""
    metrics = {}

    for gender_form_value, gender_db_value in Gender_Mapping.items():
        filtered_responses = survey_responses[survey_responses['gender'] == gender_form_value]

        if filtered_responses.empty:
            metrics[f'gender_{gender_db_value.lower()}_dei_score'] = Decimal('0.00')
            metrics[f'gender_{gender_db_value.lower()}_positive_pct'] = Decimal('0.00')
            metrics[f'gender_{gender_db_value.lower()}_neutral_pct'] = Decimal('0.00')
            metrics[f'gender_{gender_db_value.lower()}_negative_pct'] = Decimal('0.00')
            continue

        positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
            filtered_responses, list(range(20))
        )

        dei_score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)

        metrics[f'gender_{gender_db_value.lower()}_dei_score'] = dei_score
        metrics[f'gender_{gender_db_value.lower()}_positive_pct'] = positive_pct
        metrics[f'gender_{gender_db_value.lower()}_neutral_pct'] = neutral_pct
        metrics[f'gender_{gender_db_value.lower()}_negative_pct'] = negative_pct

    return metrics

def calculate_strategic_alignment_gender_metrics(survey_responses):
    """Calculate strategic alignment metrics broken down by gender."""
    metrics = {}
    
    # Calculate for each gender
    for gender in ["Male", "Female", "Other"]:
        # Filter responses for this gender
        gender_responses = survey_responses[survey_responses['gender'] == gender]
        
        if gender_responses.empty:
            # Set default values if no responses for this gender
            gender_key = gender.lower()
            metrics[f'strategic_alignment_positive_{gender_key}_pct'] = Decimal('0.00')
            metrics[f'strategic_alignment_neutral_{gender_key}_pct'] = Decimal('0.00')
            metrics[f'strategic_alignment_negative_{gender_key}_pct'] = Decimal('0.00')
            continue
            
        # Calculate sentiment percentages for strategic alignment questions for this gender
        positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
            gender_responses,
            STRATEGIC_ALIGNMENT_QUESTIONS
        )
        
        # Store the percentages with appropriate keys
        gender_key = gender.lower()
        metrics[f'strategic_alignment_positive_{gender_key}_pct'] = positive_pct
        metrics[f'strategic_alignment_neutral_{gender_key}_pct'] = neutral_pct
        metrics[f'strategic_alignment_negative_{gender_key}_pct'] = negative_pct
    
    return metrics

def update_company_scores(company_scores_model, survey_response_model, payment_model):
    """Update company scores based on survey responses."""
    successful_payments = payment_model.objects.filter(payment_status='success')
    updated_count = 0

    for pay in successful_payments:
        survey_responses = survey_response_model.objects.filter(survey_url=pay.survey_url)

        if not survey_responses.exists():
            continue

        all_metrics = calculate_all_metrics(survey_responses)

        for category, category_metrics in all_metrics.items():
            for key, value in category_metrics.items():
                setattr(company_scores_model, f'{category}_{key}', value)

        company_scores_model.save()
        updated_count += 1

    return updated_count









def calculate_live_survey_timer(start_date, end_date):
    """Calculate the remaining time for the survey in HH:MM:SS format.
    Args:
        start_date: Survey start date as datetime object
        end_date: Survey end date as datetime object
    Returns:
        String in format 'HH:MM:SS' representing remaining time,
        or '00:00:00' if survey has ended or dates are invalid
    """
    try:
        if not start_date or not end_date:
            return '00:00:00'
            
        now = datetime.now()
        
        # If survey hasn't started yet or has ended
        if now < start_date or now > end_date:
            return '00:00:00'
        
        # Calculate remaining time
        time_remaining = end_date - now
        
        # Convert to hours, minutes, seconds
        total_seconds = int(time_remaining.total_seconds())
        hours = total_seconds // 3600
        minutes = (total_seconds % 3600) // 60
        seconds = total_seconds % 60
        
        return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
    except Exception as e:
        print(f"Error calculating timer: {str(e)}")
        return '00:00:00'

