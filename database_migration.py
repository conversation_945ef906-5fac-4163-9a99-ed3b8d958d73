"""
Database Migration Script for Category Prediction Feature

This script adds the predicted_categories column to the student_data table
to store AI-predicted categories for each survey response.
"""

import mysql.connector
from mysql.connector import <PERSON>rror

def get_db_connection():
    """Create database connection"""
    try:
        return mysql.connector.connect(
            host="fz-website-registration.c9uy0agkwmgg.ap-south-1.rds.amazonaws.com",
            user="admin",
            password="master123",
            database="registration"
        )
    except Error as e:
        print(f"Error connecting to database: {e}")
        raise

def add_predicted_categories_column():
    """Add predicted_categories column to student_data table"""
    connection = None
    cursor = None
    
    try:
        connection = get_db_connection()
        cursor = connection.cursor()
        
        # Check if column already exists
        cursor.execute("""
            SELECT COLUMN_NAME 
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = 'registration' 
            AND TABLE_NAME = 'student_data' 
            AND COLUMN_NAME = 'predicted_categories'
        """)
        
        if cursor.fetchone():
            print("Column 'predicted_categories' already exists in student_data table.")
            return True
        
        # Add the column
        cursor.execute("""
            ALTER TABLE student_data 
            ADD COLUMN predicted_categories TEXT 
            COMMENT 'AI-predicted categories for the survey response'
        """)
        
        connection.commit()
        print("Successfully added 'predicted_categories' column to student_data table.")
        return True
        
    except Error as e:
        print(f"Error adding column: {e}")
        if connection:
            connection.rollback()
        return False
        
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

def create_category_analytics_table():
    """Create a new table for category analytics if it doesn't exist"""
    connection = None
    cursor = None
    
    try:
        connection = get_db_connection()
        cursor = connection.cursor()
        
        # Create category_analytics table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS category_analytics (
                id INT AUTO_INCREMENT PRIMARY KEY,
                survey_url VARCHAR(255) NOT NULL,
                category_name VARCHAR(100) NOT NULL,
                question_count INT DEFAULT 0,
                positive_responses INT DEFAULT 0,
                neutral_responses INT DEFAULT 0,
                negative_responses INT DEFAULT 0,
                total_responses INT DEFAULT 0,
                positive_percentage DECIMAL(5,2) DEFAULT 0.00,
                neutral_percentage DECIMAL(5,2) DEFAULT 0.00,
                negative_percentage DECIMAL(5,2) DEFAULT 0.00,
                category_score DECIMAL(5,2) DEFAULT 0.00,
                last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_survey_url (survey_url),
                INDEX idx_category_name (category_name),
                UNIQUE KEY unique_survey_category (survey_url, category_name)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            COMMENT='Analytics table for AI-predicted categories'
        """)
        
        connection.commit()
        print("Successfully created 'category_analytics' table.")
        return True
        
    except Error as e:
        print(f"Error creating category_analytics table: {e}")
        if connection:
            connection.rollback()
        return False
        
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

def run_migration():
    """Run all migration steps"""
    print("Starting database migration for category prediction feature...")
    
    # Step 1: Add predicted_categories column
    if not add_predicted_categories_column():
        print("Failed to add predicted_categories column. Stopping migration.")
        return False
    
    # Step 2: Create category analytics table
    if not create_category_analytics_table():
        print("Failed to create category_analytics table. Stopping migration.")
        return False
    
    print("Database migration completed successfully!")
    return True

if __name__ == "__main__":
    run_migration()
