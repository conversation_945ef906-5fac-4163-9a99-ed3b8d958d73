from crewai import Crew, Agent, Task, LLM
from dotenv import load_dotenv
import os

# Load environment variables
load_dotenv()

# Set up Groq LLM
llm = LLM(
    model="groq/meta-llama/llama-4-scout-17b-16e-instruct",
    temperature=0.4
)

# 🔹 Agent 1: Sentiment Classifier
sentiment_agent = Agent(
    role="Sentiment Analyst",
    goal="Classify a workplace-related sentence as Positive, Negative, or Neutral",
    backstory="You are a specialist in analyzing emotional tone in workplace feedback. Your task is to identify the sentiment behind employee statements.",
    verbose=True,
    allow_delegation=False,
    llm=llm
)

# 🔹 Agent 2: Category Classifier
category_agent = Agent(
    role="Category Classifier",
    goal="Classify a workplace-related sentence into one or more categories",
    backstory="You are a DEI and workplace structure expert who classifies employee feedback into relevant workplace categories for analysis.",
    verbose=True,
    allow_delegation=False,
    llm=llm
)

# 🔹 Agent 3: Sentiment Explanation Agent
sentiment_explainer_agent = Agent(
    role="Sentiment Explainer",
    goal="Explain why a specific sentiment (Positive, Negative, or Neutral) was predicted for a workplace-related sentence",
    backstory="You are a reasoning expert who explains how sentiment classifications were made for workplace feedback. Use sentence tone and keywords to justify the result briefly.",
    verbose=True,
    allow_delegation=False,
    llm=llm
)

# 🔸 Prompt template for sentiment
sentiment_prompt = """
You are a highly experienced workplace sentiment analyst and DEI (Diversity, Equity, Inclusion) specialist. 
Your task is to carefully assess the emotional tone of employee feedback or workplace-related sentences.

🎯 Your goal:
Classify the overall **sentiment** of the sentence as one of the following:
- Positive
- Negative
- Neutral

🧠 How to evaluate:
- Be **extra cautious** when dealing with **sensitive topics** like discrimination, harassment, unequal treatment, or exclusion. 
  If the tone even slightly implies discomfort or unfairness, classify it as **Negative**.
- Consider **implicit intent** and emotional undercurrents. Some negative sentiments may be wrapped in polite or vague phrasing.

🛑 Output Format:
Respond with only one word from this list: Positive, Negative, or Neutral. Do not include labels like "Sentiment:" or any other text.


📚 Examples (with reasoning):

Sentence: "I feel safe expressing my identity at work and my manager supports my growth."  
 Sentiment: Positive  
Reason: The sentence expresses psychological safety and active leadership support — a clear positive experience.

Sentence: "Leadership says they support inclusion, but I haven’t seen any action."  
Sentiment: Negative  
Reason: Indicates disillusionment and perceived inaction from leadership despite stated values.

Sentence: "Most policies are fine, but there's still a sense of favoritism in team recognition."  
Sentiment: Neutral  
Reason: Balanced feedback — some concerns, but not entirely negative or positive.

Sentence: "Some employees report feeling isolated after raising harassment concerns."  
Sentiment: Negative  
Reason: Any statement mentioning harassment or exclusion must be treated as emotionally sensitive and classified as negative.

---

Now classify the following sentence:

Sentence: "{sentence}"
"""

# 🔸 Prompt template for category
category_prompt = """
Classify the following workplace-related sentence into one or more of these categories:
Diversity, Equity, Inclusion, Leadership, Policy, Workplace Culture, Employee Engagement,
strategic_alignment, culture_engagement, support_motivation, skill_development

🛑 Only respond with a comma-separated list of relevant categories. No explanations or extra text.

Sentence: "{sentence}"
"""
# 🔸 Prompt template for explanation
explanation_prompt = """
🧠 You are a highly experienced sentiment reasoning expert trained in workplace psychology, DEI, and behavioral analysis.

Your task is to **explain why the following sentence was labeled as "{sentiment}"**.

🎯 Focus areas:
- Emotional tone and language (e.g., frustration, optimism, hesitation)
- Implicit meaning and intent behind the words
- Contextual clues such as fairness, support, safety, exclusion, or leadership dynamics

---

📌 Sentence:
"{sentence}"

✅ Output:
Provide a brief and thoughtful 1–2 line explanation **justifying** why this sentiment is accurate — based on the sentence’s tone, emotion, or underlying message. Be specific, avoid repeating the sentence.

💡 Example:
- If the sentence implies support and psychological safety → "The sentence expresses emotional safety and inclusion, which are clearly positive indicators."
- If it suggests disillusionment or subtle exclusion → "Despite neutral wording, there's an underlying tone of disappointment with leadership's lack of action."

Respond only with your reasoning below:
"""

# ✅ Function 1: Run Sentiment Prediction
def predict_sentiment(sentence):
    task = Task(
        description=sentiment_prompt.format(sentence=sentence),
        expected_output="Sentiment: Positive, Negative, or Neutral",
        agent=sentiment_agent
    )
    crew = Crew(
        agents=[sentiment_agent],
        tasks=[task],
        verbose=False
    )
    result = crew.kickoff()
    return str(result).strip()  # Convert CrewOutput to string


# ✅ Function 2: Run Category Prediction
def predict_category(sentence):
    task = Task(
        description=category_prompt.format(sentence=sentence),
        expected_output="Comma-separated list of categories",
        agent=category_agent
    )
    crew = Crew(
        agents=[category_agent],
        tasks=[task],
        verbose=False
    )
    result = crew.kickoff()
    return str(result).strip()



# ✅ Function 3: Explain Why That Sentiment Was Chosen
def explain_sentiment(sentence):
    task = Task(
        description=sentiment_prompt.format(sentence=sentence),
        expected_output="Here tell me the reason why you have predicted this sentiment for the sentence",
        agent=sentiment_explainer_agent
    )
    crew = Crew(
        agents=[sentiment_explainer_agent],
        tasks=[task],
        verbose=False
    )
    result = crew.kickoff()
    return str(result).strip()

# ✅ Function 4: Voice Statement Matching
voice_matching_prompt = """
You are an expert in workplace sentiment analysis. Your task is to match survey questions to employee voice statements.

Given a survey question, determine which of these employee voice statements it most closely relates to:

1. "I feel safe to speak up without fear of negative consequences."
2. "Leaders actively listen and follow through on employee feedback"
3. "The company consistently lives by its stated values"
4. "Our internal culture aligns with what we promote externally"
5. "Leadership communicates with transparency and honesty"
6. "I trust leaders to make fair and thoughtful decisions"
7. "I feel a genuine sense of belonging in the organization"
8. "My unique identity and contributions are respected and valued"
9. "My work is recognized, and I feel appreciated"
10. "I see clear opportunities for growth and development here"
11. "I have a healthy work-life balance and feel supported"
12. "The company genuinely cares about my mental and emotional well-being"
13. "Promotions and rewards are handled fairly and transparently"

🛑 Instructions:
1. Analyze the survey question carefully
2. Find the voice statement that best matches the question's theme
3. Only respond with the number (1-13) of the matching voice statement
4. If no clear match exists, respond with "0"
5. No explanations or extra text

Survey Question: "{question}"
"""

def predict_voice_statement(question_text):
    """Predict which employee voice statement a survey question relates to."""
    task = Task(
        description=voice_matching_prompt.format(question=question_text),
        expected_output="Single number (1-13) or 0 if no match",
        agent=category_agent
    )
    crew = Crew(
        agents=[category_agent],
        tasks=[task],
        verbose=False
    )
    result = crew.kickoff()

    try:
        voice_num = int(str(result).strip())
        return voice_num if 1 <= voice_num <= 13 else 0
    except ValueError:
        return 0

# 🧪 Test Case
if __name__ == "__main__":
    test_sentence = "Is there any power harassment in your workplace?,Medium"

    sentiment = predict_sentiment(test_sentence)
    categories = predict_category(test_sentence)
    voice_statement = predict_voice_statement(test_sentence)
    explanation = explain_sentiment(test_sentence)

    print("\n🧠 Sentiment Prediction:", sentiment)
    print("📂 Category Prediction:", categories)
    print("🎤 Voice Statement Match:", voice_statement)
    print("💡 Explanation:", explanation)
