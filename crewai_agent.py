from crewai import Crew, Agent, Task, LLM
from dotenv import load_dotenv
import os

# Load environment variables
load_dotenv()

# Set up Groq LLM
llm = LLM(
    model="groq/meta-llama/llama-4-scout-17b-16e-instruct",
    temperature=0.4
)

# 🔹 Agent 1: Sentiment Classifier
sentiment_agent = Agent(
    role="Sentiment Analyst",
    goal="Classify a workplace-related sentence as Positive, Negative, or Neutral",
    backstory="You are a specialist in analyzing emotional tone in workplace feedback. Your task is to identify the sentiment behind employee statements.",
    verbose=True,
    allow_delegation=False,
    llm=llm
)

# 🔹 Agent 2: Category Classifier
category_agent = Agent(
    role="Category Classifier",
    goal="Classify a workplace-related sentence into one or more categories",
    backstory="You are a DEI and workplace structure expert who classifies employee feedback into relevant workplace categories for analysis.",
    verbose=True,
    allow_delegation=False,
    llm=llm
)

# 🔹 Agent 3: Sentiment Explanation Agent
sentiment_explainer_agent = Agent(
    role="Sentiment Explainer",
    goal="Explain why a specific sentiment (Positive, Negative, or Neutral) was predicted for a workplace-related sentence",
    backstory="You are a reasoning expert who explains how sentiment classifications were made for workplace feedback. Use sentence tone and keywords to justify the result briefly.",
    verbose=True,
    allow_delegation=False,
    llm=llm
)

# 🔸 Prompt template for sentiment
sentiment_prompt = """
You are a highly experienced workplace sentiment analyst and DEI (Diversity, Equity, Inclusion) specialist. 
Your task is to carefully assess the emotional tone of employee feedback or workplace-related sentences.

🎯 Your goal:
Classify the overall **sentiment** of the sentence as one of the following:
- Positive
- Negative
- Neutral

🧠 How to evaluate:
- Be **extra cautious** when dealing with **sensitive topics** like discrimination, harassment, unequal treatment, or exclusion. 
  If the tone even slightly implies discomfort or unfairness, classify it as **Negative**.
- Consider **implicit intent** and emotional undercurrents. Some negative sentiments may be wrapped in polite or vague phrasing.

🛑 Output Format:
Respond with only one word from this list: Positive, Negative, or Neutral. Do not include labels like "Sentiment:" or any other text.


📚 Examples (with reasoning):

Sentence: "I feel safe expressing my identity at work and my manager supports my growth."  
 Sentiment: Positive  
Reason: The sentence expresses psychological safety and active leadership support — a clear positive experience.

Sentence: "Leadership says they support inclusion, but I haven’t seen any action."  
Sentiment: Negative  
Reason: Indicates disillusionment and perceived inaction from leadership despite stated values.

Sentence: "Most policies are fine, but there's still a sense of favoritism in team recognition."  
Sentiment: Neutral  
Reason: Balanced feedback — some concerns, but not entirely negative or positive.

Sentence: "Some employees report feeling isolated after raising harassment concerns."  
Sentiment: Negative  
Reason: Any statement mentioning harassment or exclusion must be treated as emotionally sensitive and classified as negative.

---

Now classify the following sentence:

Sentence: "{sentence}"
"""

# 🔸 Prompt template for category
category_prompt = """
You are an expert workplace analyst specializing in categorizing employee feedback and survey responses.

Classify the following workplace-related sentence into one or more of these specific categories:

**Primary Categories:**
- diversity: Questions about representation, demographics, and diverse perspectives
- equity: Questions about fairness, equal opportunities, and unbiased treatment
- inclusion: Questions about belonging, acceptance, and feeling valued
- leadership: Questions about management effectiveness, leadership quality, and guidance
- workplace_culture: Questions about company values, environment, and cultural aspects
- employee_engagement: Questions about motivation, involvement, and connection to work

**Secondary Categories:**
- strategic_alignment: Questions about company direction, vision, and goal alignment
- culture_engagement: Questions about cultural participation and engagement
- support_motivation: Questions about support systems and motivational factors
- skill_development: Questions about learning, growth, and career development
- credibility: Questions about trust, reliability, and organizational credibility
- fairness: Questions about just treatment and equitable processes
- workplace_satisfaction: Questions about job satisfaction and work environment
- team_spirit: Questions about collaboration and team dynamics
- well_being: Questions about mental health, work-life balance, and employee wellness
- respect: Questions about mutual respect and professional treatment
- open_communication: Questions about transparency and communication channels
- recognition: Questions about acknowledgment and appreciation
- motivation: Questions about drive, inspiration, and work motivation

**Action Categories:**
- communication: Questions about internal communication effectiveness
- leadership_effectiveness: Questions about leadership performance and impact
- work_life_balance: Questions about balance between work and personal life
- career_development: Questions about professional growth opportunities
- recognition_rewards: Questions about reward systems and recognition programs
- workplace_environment: Questions about physical and cultural work environment
- inclusion_diversity: Questions combining inclusion and diversity aspects
- compensation_transparency: Questions about pay equity and transparency
- feedback_mechanisms: Questions about feedback systems and processes
- organizational_transparency: Questions about company openness and transparency
- manager_employee_relationship: Questions about manager-employee interactions
- psychological_safety: Questions about safety to express opinions and concerns
- mission_values_alignment: Questions about alignment with company mission and values
- innovation_creativity: Questions about creative thinking and innovation

🛑 Instructions:
1. Analyze the sentence carefully for its main themes
2. Select 1-3 most relevant categories (avoid over-categorization)
3. Prioritize primary categories, then secondary, then action categories
4. Only respond with a comma-separated list of category names
5. Use exact category names from the list above
6. No explanations or extra text

Sentence: "{sentence}"
"""
# 🔸 Prompt template for explanation
explanation_prompt = """
🧠 You are a highly experienced sentiment reasoning expert trained in workplace psychology, DEI, and behavioral analysis.

Your task is to **explain why the following sentence was labeled as "{sentiment}"**.

🎯 Focus areas:
- Emotional tone and language (e.g., frustration, optimism, hesitation)
- Implicit meaning and intent behind the words
- Contextual clues such as fairness, support, safety, exclusion, or leadership dynamics

---

📌 Sentence:
"{sentence}"

✅ Output:
Provide a brief and thoughtful 1–2 line explanation **justifying** why this sentiment is accurate — based on the sentence’s tone, emotion, or underlying message. Be specific, avoid repeating the sentence.

💡 Example:
- If the sentence implies support and psychological safety → "The sentence expresses emotional safety and inclusion, which are clearly positive indicators."
- If it suggests disillusionment or subtle exclusion → "Despite neutral wording, there's an underlying tone of disappointment with leadership's lack of action."

Respond only with your reasoning below:
"""

# ✅ Function 1: Run Sentiment Prediction
def predict_sentiment(sentence):
    task = Task(
        description=sentiment_prompt.format(sentence=sentence),
        expected_output="Sentiment: Positive, Negative, or Neutral",
        agent=sentiment_agent
    )
    crew = Crew(
        agents=[sentiment_agent],
        tasks=[task],
        verbose=False
    )
    result = crew.kickoff()
    return str(result).strip()  # Convert CrewOutput to string


# ✅ Function 2: Run Category Prediction
def predict_category(sentence):
    task = Task(
        description=category_prompt.format(sentence=sentence),
        expected_output="Comma-separated list of categories",
        agent=category_agent
    )
    crew = Crew(
        agents=[category_agent],
        tasks=[task],
        verbose=False
    )
    result = crew.kickoff()
    return str(result).strip()

# ✅ Function 4: Process and Validate Categories
def process_predicted_categories(raw_categories):
    """
    Process the raw category prediction output and validate against known categories.
    Returns a cleaned list of valid categories.
    """
    # Define all valid categories
    valid_categories = {
        # Primary Categories
        'diversity', 'equity', 'inclusion', 'leadership', 'workplace_culture', 'employee_engagement',
        # Secondary Categories
        'strategic_alignment', 'culture_engagement', 'support_motivation', 'skill_development',
        'credibility', 'fairness', 'workplace_satisfaction', 'team_spirit', 'well_being',
        'respect', 'open_communication', 'recognition', 'motivation',
        # Action Categories
        'communication', 'leadership_effectiveness', 'work_life_balance', 'career_development',
        'recognition_rewards', 'workplace_environment', 'inclusion_diversity',
        'compensation_transparency', 'feedback_mechanisms', 'organizational_transparency',
        'manager_employee_relationship', 'psychological_safety', 'mission_values_alignment',
        'innovation_creativity'
    }

    # Clean and split the categories
    if isinstance(raw_categories, str):
        categories = [cat.strip().lower() for cat in raw_categories.split(',')]
    else:
        categories = []

    # Filter valid categories
    valid_predicted_categories = [cat for cat in categories if cat in valid_categories]

    # If no valid categories found, assign a default
    if not valid_predicted_categories:
        valid_predicted_categories = ['employee_engagement']  # Default fallback

    return valid_predicted_categories

# ✅ Function 5: Get Category Mappings for Question Numbers
def get_question_category_mappings():
    """
    Returns a mapping of categories to their corresponding question numbers
    based on the existing category definitions in survey_metrics_calculator.py
    """
    return {
        'diversity': [1, 2, 3, 13, 19],
        'equity': [4, 5, 6, 11, 16, 17, 18],
        'inclusion': [7, 8, 9, 10, 12, 14, 15, 20],
        'culture_engagement': [3, 7, 8, 9, 10, 15, 19, 20],
        'strategic_alignment': [11, 19],
        'support_motivation': [2, 4, 5, 6, 8, 9, 10, 13],
        'skill_development': [4, 16, 18],
        'credibility': [5, 8, 11, 13, 15],
        'fairness': [1, 2, 5, 12, 13, 14, 16, 18],
        'workplace_satisfaction': [7, 9, 10, 16, 17],
        'team_spirit': [3, 8, 9, 14, 15, 19, 20],
        'well_being': [7, 12, 17],
        'respect': [2, 3, 8, 9, 10, 12, 14, 18, 19],
        'open_communication': [14],
        'recognition': [3, 9, 10, 14, 16, 19],
        'motivation': [4, 6, 10, 16, 18],
        'leadership': [9, 12, 13, 14],
        'workplace_culture': [1, 2, 3, 4, 5, 6, 8, 11, 15, 16, 17],
        # Action categories
        'communication': [3, 8, 14, 19, 20],
        'leadership_effectiveness': [1, 3, 4, 5, 6, 8, 9, 11, 12, 13, 15],
        'work_life_balance': [7, 17],
        'career_development': [1, 4, 6],
        'recognition_rewards': [10],
        'employee_engagement': [2, 3, 8, 9, 10, 15, 19],
        'workplace_environment': [1, 2, 3, 5, 8, 9, 12, 13, 17, 18, 20],
        'inclusion_diversity': [1, 3, 5, 12, 14, 16, 18, 19],
        'compensation_transparency': [16],
        'feedback_mechanisms': [8, 12, 14, 19],
        'organizational_transparency': [1, 14, 15, 16, 19],
        'manager_employee_relationship': [5, 6, 8, 13, 17],
        'psychological_safety': [2, 3, 9, 10, 12],
        'mission_values_alignment': [4, 11, 18, 20],
        'innovation_creativity': [7]
    }

# ✅ Function 3: Explain Why That Sentiment Was Chosen
def explain_sentiment(sentence):
    task = Task(
        description=sentiment_prompt.format(sentence=sentence),
        expected_output="Here tell me the reason why you have predicted this sentiment for the sentence",
        agent=sentiment_explainer_agent
    )
    crew = Crew(
        agents=[sentiment_explainer_agent],
        tasks=[task],
        verbose=False
    )
    result = crew.kickoff()
    return str(result).strip()

# 🧪 Test Case
if __name__ == "__main__":
    test_sentence = "Is there any power harassment in your workplace?,Medium"

    sentiment = predict_sentiment(test_sentence)
    categories = predict_category(test_sentence)
    processed_categories = process_predicted_categories(categories)
    explanation = explain_sentiment(test_sentence)

    print("\n🧠 Sentiment Prediction:", sentiment)
    print("📂 Raw Category Prediction:", categories)
    print("✅ Processed Categories:", processed_categories)
    print("💡 Explanation:", explanation)
